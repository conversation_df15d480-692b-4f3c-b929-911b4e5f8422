stages:
  - build
  - test
  - security
  - package
  - deploy

variables:
  DOCKER_IMAGE_NAME: "veasy-api"
  DOCKER_REGISTRY: "$CI_REGISTRY"
  DOTNET_VERSION: "9.0"
  
# Cache for faster builds
cache:
  paths:
    - ~/.nuget/packages/

# Build stage
build:
  stage: build
  image: mcr.microsoft.com/dotnet/sdk:9.0
  before_script:
    - dotnet --version
  script:
    - dotnet restore
    - dotnet build --configuration Release --no-restore
  artifacts:
    paths:
      - "*/bin/Release/net9.0/"
      - "*/obj/"
    expire_in: 1 hour
  only:
    - main
    - develop
    - merge_requests

# Unit tests
test:unit:
  stage: test
  image: mcr.microsoft.com/dotnet/sdk:9.0
  dependencies:
    - build
  script:
    - dotnet test --configuration Release --no-build --collect:"XPlat Code Coverage" --logger trx --results-directory ./TestResults/
  artifacts:
    when: always
    paths:
      - ./TestResults/
    reports:
      junit: ./TestResults/*.trx
      coverage_report:
        coverage_format: cobertura
        path: ./TestResults/*/coverage.cobertura.xml
    expire_in: 1 week
  coverage: '/Total\s*\|\s*(\d+(?:\.\d+)?)%/'
  only:
    - main
    - develop
    - merge_requests

# Security scanning
security:sast:
  stage: security
  image: mcr.microsoft.com/dotnet/sdk:9.0
  dependencies:
    - build
  script:
    - dotnet list package --vulnerable --include-transitive || true
    - dotnet list package --deprecated || true
  artifacts:
    reports:
      sast: gl-sast-report.json
  allow_failure: true
  only:
    - main
    - develop
    - merge_requests

# Docker build and push
docker:build:
  stage: package
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  dependencies:
    - build
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - docker build -t $CI_REGISTRY_IMAGE/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA .
    - docker build -t $CI_REGISTRY_IMAGE/$DOCKER_IMAGE_NAME:latest .
    - docker push $CI_REGISTRY_IMAGE/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE/$DOCKER_IMAGE_NAME:latest
  only:
    - main
    - develop

# Deploy to staging
deploy:staging:
  stage: deploy
  image: alpine:latest
  dependencies:
    - docker:build
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Deploying to staging environment..."
    - |
      curl -X POST "$STAGING_WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{
          \"image\": \"$CI_REGISTRY_IMAGE/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA\",
          \"environment\": \"staging\",
          \"commit\": \"$CI_COMMIT_SHA\",
          \"branch\": \"$CI_COMMIT_REF_NAME\"
        }"
  environment:
    name: staging
    url: $STAGING_URL
  only:
    - develop

# Deploy to production
deploy:production:
  stage: deploy
  image: alpine:latest
  dependencies:
    - docker:build
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Deploying to production environment..."
    - |
      curl -X POST "$PRODUCTION_WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{
          \"image\": \"$CI_REGISTRY_IMAGE/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA\",
          \"environment\": \"production\",
          \"commit\": \"$CI_COMMIT_SHA\",
          \"branch\": \"$CI_COMMIT_REF_NAME\"
        }"
  environment:
    name: production
    url: $PRODUCTION_URL
  when: manual
  only:
    - main

# Clean up old images
cleanup:
  stage: deploy
  image: alpine:latest
  script:
    - echo "Cleanup completed"
  when: always
  only:
    - main
    - develop