{"version": "0.2.0", "configurations": [{"name": ".NET Core Launch (web)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/VeasyApiDotNet/bin/Debug/net9.0/VeasyApiDotNet.dll", "args": [], "cwd": "${workspaceFolder}/VeasyApiDotNet", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}]}