# Chat Completion API Setup Guide

This document provides instructions for setting up and configuring the comprehensive chat completion API that has been added to the Veasy API project.

## Overview

The chat completion API provides the following features:

- **JWT Bearer Authentication** - Integration with external Identity Server as OAuth2 resource server
- **Chat Completion Endpoints** - Create chat completions with OpenAI integration
- **Chat History Management** - Store and retrieve chat conversations
- **Database Integration** - PostgreSQL with Entity Framework Core
- **Function Calling Support** - OpenAI tools/function calling capability (placeholder implementation)
- **Streaming Support** - Server-sent events for real-time responses (placeholder implementation)

## Database Setup

### 1. PostgreSQL Installation

Install PostgreSQL on your system:

```bash
# macOS (using Homebrew)
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# Windows
# Download and install from https://www.postgresql.org/download/windows/
```

### 2. Create Database

```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database and user
CREATE DATABASE veasy_chat_dev;
CREATE USER veasy_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE veasy_chat_dev TO veasy_user;
```

### 3. Run Database Migrations

```bash
# Navigate to the API project directory
cd VeasyApiDotNet

# Install EF Core tools (if not already installed)
dotnet tool install --global dotnet-ef

# Update database with migrations
export PATH="$PATH:/Users/<USER>/.dotnet/tools"  # Add to PATH if needed
dotnet ef database update --context ChatDbContext
```

## Configuration

### 1. Update Connection String

Edit `appsettings.Development.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=veasy_chat_dev;Username=veasy_user;Password=your_secure_password;Port=5432"
  }
}
```

### 2. Configure OpenAI API

Add your OpenAI API key to `appsettings.Development.json`:

```json
{
  "OpenAI": {
    "ApiKey": "your-openai-api-key-here"
  }
}
```

### 3. Configure JWT Authentication

Update the JWT settings in `appsettings.Development.json`:

```json
{
  "Authentication": {
    "Jwt": {
      "Authority": "https://your-identity-server.com",
      "Audience": "veasy-api",
      "RequireHttpsMetadata": false,
      "IsEnabled": true
    }
  }
}
```

**Note**: Set `IsEnabled` to `false` to disable JWT authentication during development.

## API Endpoints

### Chat Completion

```http
POST /api/chat/completions
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "model": "gpt-4o-mini",
  "maxTokens": 1000,
  "temperature": 0.7,
  "title": "My Chat"
}
```

### Streaming Chat Completion

```http
POST /api/chat/completions/stream
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "messages": [
    {
      "role": "user", 
      "content": "Tell me a story"
    }
  ],
  "stream": true
}
```

### Get Chat History

```http
GET /api/chat/{chatId}
Authorization: Bearer <jwt-token>
```

### Get User's Chat Histories

```http
GET /api/chat/history?page=1&pageSize=20&search=keyword
Authorization: Bearer <jwt-token>
```

### Update Chat History

```http
PUT /api/chat/{chatId}
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "title": "Updated Chat Title",
  "description": "Updated description"
}
```

### Delete Chat History

```http
DELETE /api/chat/{chatId}
Authorization: Bearer <jwt-token>
```

## Running the Application

```bash
# Navigate to the API project
cd VeasyApiDotNet

# Restore packages
dotnet restore

# Run the application
dotnet run
```

The API will be available at:
- HTTP: `http://localhost:5134`
- HTTPS: `https://localhost:7134`
- API Documentation: `http://localhost:5134/scalar`

## Development Notes

### Current Implementation Status

1. **✅ Complete**: Database schema, Entity Framework setup, API endpoints, JWT configuration
2. **🚧 Placeholder**: OpenAI integration (needs proper SDK implementation)
3. **🚧 Placeholder**: Streaming responses (basic implementation)
4. **🚧 Placeholder**: Function calling (structure in place)

### Next Steps for OpenAI Integration

The current OpenAI integration uses placeholder responses. To implement actual OpenAI calls:

1. Update the `ChatService.CreateChatCompletionAsync` method to use the correct OpenAI SDK API
2. Implement proper streaming in `CreateStreamingChatCompletionAsync`
3. Add function calling support with proper tool definitions

### Testing

Use tools like Postman, curl, or the Scalar API documentation interface to test the endpoints.

Example curl command:

```bash
curl -X POST "http://localhost:5134/api/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "Hello!"
      }
    ]
  }'
```

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure PostgreSQL is running and connection string is correct
2. **JWT Authentication**: Verify Identity Server configuration and token format
3. **OpenAI API**: Check API key configuration and network connectivity
4. **Migrations**: Ensure EF Core tools are installed and PATH is configured

### Logs

Check application logs for detailed error information. The application uses structured logging with different log levels for development and production.
