# Use the official .NET 9.0 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy solution file
COPY veasy-api-dotnet.sln .

# Copy project files
COPY VeasyApi.Models/VeasyApi.Models.csproj VeasyApi.Models/
COPY VeasyApi.Domain/VeasyApi.Domain.csproj VeasyApi.Domain/
COPY VeasyApi.Service/VeasyApi.Service.csproj VeasyApi.Service/
COPY VeasyApiDotNet/VeasyApiDotNet.csproj VeasyApiDotNet/

# Restore dependencies
RUN dotnet restore

# Copy all source code
COPY . .

# Build the application
RUN dotnet build -c Release --no-restore

# Publish the application
RUN dotnet publish VeasyApiDotNet/VeasyApiDotNet.csproj -c Release -o /app/publish --no-restore

# Use the official .NET 9.0 runtime image for the final image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy the published application
COPY --from=build /app/publish .

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Set environment variables
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

# Start the application
ENTRYPOINT ["dotnet", "VeasyApiDotNet.dll"]