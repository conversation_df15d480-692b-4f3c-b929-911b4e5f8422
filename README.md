# Veasy API - .NET 9 RAG Application

## Architecture

This solution follows a clean layered architecture:

- **VeasyApi.Models** - Data models and DTOs
- **VeasyApi.Domain** - Business logic interfaces and abstractions
- **VeasyApi.Service** - Service implementations and business logic
- **VeasyApiDotNet** - Web API layer (controllers, middleware, configuration)

## Prerequisites

- .NET 9.0 SDK
- Docker (optional)
- OpenAI API Key

## Configuration

Set the following environment variables or update `appsettings.json`:

```json
{
  "OpenAI": {
    "ApiKey": "your-openai-api-key",
    "Embedding": {
      "ModelName": "text-embedding-3-small",
      "EmbeddingDimension": 1536
    },
    "LLM": {
      "ModelName": "gpt-4o-mini",
      "MaxContextLength": 128000
    }
  },
  "Authentication": {
    "ApiKey": "your-api-key"
  }
}
```

## Running the Application

### Local Development
```bash
dotnet restore
dotnet build
dotnet run --project VeasyApiDotNet
```

### Docker
```bash
docker build -t veasy-api .
docker run -p 8080:8080 -e OpenAI__ApiKey=your-key veasy-api
```

## API Documentation

Once running, access the Scalar API documentation at:
- Local: http://localhost:5134/scalar
- Docker: http://localhost:8080/scalar

## Project Structure

```
├── VeasyApi.Models/          # Data models
├── VeasyApi.Domain/          # Business interfaces
├── VeasyApi.Service/         # Service implementations
├── VeasyApiDotNet/          # Web API
├── Dockerfile               # Container configuration
└── .gitlab-ci.yml          # CI/CD pipeline
```

## CI/CD Pipeline

The GitLab CI/CD pipeline includes:
- Build and compile
- Unit testing with coverage
- Security scanning
- Docker image building
- Automated deployment to staging/production