using VeasyApi.Models;

namespace VeasyApi.Domain.Abstractions;

/// <summary>
/// Interface for chat completion and history management services
/// </summary>
public interface IChatService
{
    /// <summary>
    /// Creates a new chat completion using OpenAI API
    /// </summary>
    /// <param name="request">The chat completion request</param>
    /// <param name="userId">The ID of the user making the request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The chat completion response</returns>
    Task<ChatCompletionResponse> CreateChatCompletionAsync(
        ChatCompletionRequest request, 
        string userId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a streaming chat completion using OpenAI API
    /// </summary>
    /// <param name="request">The chat completion request</param>
    /// <param name="userId">The ID of the user making the request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>An async enumerable of chat completion chunks</returns>
    IAsyncEnumerable<string> CreateStreamingChatCompletionAsync(
        Chat<PERSON><PERSON>pletionRequest request, 
        string userId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a chat history by ID
    /// </summary>
    /// <param name="chatId">The chat ID</param>
    /// <param name="userId">The ID of the user requesting the chat</param>
    /// <param name="includeMessages">Whether to include messages in the response</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The chat history or null if not found</returns>
    Task<ChatHistoryDto?> GetChatHistoryAsync(
        Guid chatId, 
        string userId, 
        bool includeMessages = true, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets paginated chat histories for a user
    /// </summary>
    /// <param name="userId">The ID of the user</param>
    /// <param name="request">The pagination and filtering request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated chat histories</returns>
    Task<ChatHistoryPagedResponse> GetChatHistoriesAsync(
        string userId, 
        ChatHistoryRequest request, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates a chat history (title, description, metadata)
    /// </summary>
    /// <param name="chatId">The chat ID</param>
    /// <param name="userId">The ID of the user updating the chat</param>
    /// <param name="title">New title (optional)</param>
    /// <param name="description">New description (optional)</param>
    /// <param name="metadata">New metadata (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The updated chat history or null if not found</returns>
    Task<ChatHistoryDto?> UpdateChatHistoryAsync(
        Guid chatId, 
        string userId, 
        string? title = null, 
        string? description = null, 
        Dictionary<string, object>? metadata = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a chat history and all its messages
    /// </summary>
    /// <param name="chatId">The chat ID</param>
    /// <param name="userId">The ID of the user deleting the chat</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if deleted successfully, false if not found</returns>
    Task<bool> DeleteChatHistoryAsync(
        Guid chatId, 
        string userId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Archives/deactivates a chat history
    /// </summary>
    /// <param name="chatId">The chat ID</param>
    /// <param name="userId">The ID of the user archiving the chat</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if archived successfully, false if not found</returns>
    Task<bool> ArchiveChatHistoryAsync(
        Guid chatId, 
        string userId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets chat messages for a specific chat
    /// </summary>
    /// <param name="chatId">The chat ID</param>
    /// <param name="userId">The ID of the user requesting the messages</param>
    /// <param name="page">Page number (1-based)</param>
    /// <param name="pageSize">Number of messages per page</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of chat messages</returns>
    Task<List<ChatMessageDetailDto>> GetChatMessagesAsync(
        Guid chatId, 
        string userId, 
        int page = 1, 
        int pageSize = 50, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Estimates token count for a list of messages
    /// </summary>
    /// <param name="messages">The messages to count tokens for</param>
    /// <param name="model">The model to use for token counting (optional)</param>
    /// <returns>Estimated token count</returns>
    int EstimateTokenCount(List<ChatMessageDto> messages, string? model = null);

    /// <summary>
    /// Validates a chat completion request
    /// </summary>
    /// <param name="request">The request to validate</param>
    /// <returns>List of validation errors (empty if valid)</returns>
    List<string> ValidateRequest(ChatCompletionRequest request);
}
