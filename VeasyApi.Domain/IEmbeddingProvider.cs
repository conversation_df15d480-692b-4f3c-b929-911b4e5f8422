using System.ComponentModel.DataAnnotations;

namespace VeasyApi.Domain.Abstractions;

/// <summary>
/// Interface for text embedding services that convert text into vector representations
/// </summary>
public interface IEmbeddingProvider
{
    /// <summary>
    /// Generates embeddings for a single text input
    /// </summary>
    /// <param name="text">The text to embed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A vector representation of the input text</returns>
    Task<float[]> GetEmbeddingAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates embeddings for multiple text inputs in batch
    /// </summary>
    /// <param name="texts">Collection of texts to embed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of vector representations corresponding to input texts</returns>
    Task<IEnumerable<float[]>> GetEmbeddingsAsync(IEnumerable<string> texts, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the dimension size of the embeddings produced by this provider
    /// </summary>
    int EmbeddingDimension { get; }

    /// <summary>
    /// Gets the name/model identifier of the embedding provider
    /// </summary>
    string ModelName { get; }
}