namespace VeasyApi.Domain.Abstractions;

/// <summary>
/// Interface for Large Language Model services that generate text responses
/// </summary>
public interface ILLMProvider
{
    /// <summary>
    /// Generates a text response based on the provided prompt
    /// </summary>
    /// <param name="prompt">The input prompt for text generation</param>
    /// <param name="maxTokens">Maximum number of tokens to generate</param>
    /// <param name="temperature">Controls randomness in generation (0.0 to 2.0)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The generated text response</returns>
    Task<string> GenerateResponseAsync(string prompt, int maxTokens = 1000, float temperature = 0.7f, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a response using a system message and user message
    /// </summary>
    /// <param name="systemMessage">The system message that sets the context/behavior</param>
    /// <param name="userMessage">The user's input message</param>
    /// <param name="maxTokens">Maximum number of tokens to generate</param>
    /// <param name="temperature">Controls randomness in generation (0.0 to 2.0)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The generated text response</returns>
    Task<string> GenerateResponseAsync(string systemMessage, string userMessage, int maxTokens = 1000, float temperature = 0.7f, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a streaming response for real-time text generation
    /// </summary>
    /// <param name="prompt">The input prompt for text generation</param>
    /// <param name="maxTokens">Maximum number of tokens to generate</param>
    /// <param name="temperature">Controls randomness in generation (0.0 to 2.0)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>An async enumerable of text chunks as they are generated</returns>
    IAsyncEnumerable<string> GenerateStreamingResponseAsync(string prompt, int maxTokens = 1000, float temperature = 0.7f, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the name/model identifier of the LLM provider
    /// </summary>
    string ModelName { get; }

    /// <summary>
    /// Gets the maximum context length supported by the model
    /// </summary>
    int MaxContextLength { get; }
}