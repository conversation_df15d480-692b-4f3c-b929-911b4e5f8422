using System.ComponentModel.DataAnnotations;

namespace VeasyApi.Models;

/// <summary>
/// Request model for chat completion API
/// </summary>
public class ChatCompletionRequest
{
    /// <summary>
    /// Optional chat ID to continue an existing conversation
    /// </summary>
    public Guid? ChatId { get; set; }

    /// <summary>
    /// The messages in the conversation
    /// </summary>
    [Required]
    [MinLength(1, ErrorMessage = "At least one message is required")]
    public List<ChatMessageDto> Messages { get; set; } = new();

    /// <summary>
    /// The model to use for completion (e.g., "gpt-4", "gpt-3.5-turbo")
    /// </summary>
    [StringLength(100)]
    public string? Model { get; set; }

    /// <summary>
    /// Maximum number of tokens to generate
    /// </summary>
    [Range(1, 32000, ErrorMessage = "MaxTokens must be between 1 and 32000")]
    public int? MaxTokens { get; set; }

    /// <summary>
    /// Controls randomness in generation (0.0 to 2.0)
    /// </summary>
    [Range(0.0, 2.0, ErrorMessage = "Temperature must be between 0.0 and 2.0")]
    public float? Temperature { get; set; }

    /// <summary>
    /// Controls diversity via nucleus sampling (0.0 to 1.0)
    /// </summary>
    [Range(0.0, 1.0, ErrorMessage = "TopP must be between 0.0 and 1.0")]
    public float? TopP { get; set; }

    /// <summary>
    /// Number of chat completion choices to generate
    /// </summary>
    [Range(1, 10, ErrorMessage = "N must be between 1 and 10")]
    public int? N { get; set; }

    /// <summary>
    /// Whether to stream the response
    /// </summary>
    public bool Stream { get; set; } = false;

    /// <summary>
    /// Up to 4 sequences where the API will stop generating further tokens
    /// </summary>
    public List<string>? Stop { get; set; }

    /// <summary>
    /// Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency
    /// </summary>
    [Range(-2.0, 2.0, ErrorMessage = "FrequencyPenalty must be between -2.0 and 2.0")]
    public float? FrequencyPenalty { get; set; }

    /// <summary>
    /// Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far
    /// </summary>
    [Range(-2.0, 2.0, ErrorMessage = "PresencePenalty must be between -2.0 and 2.0")]
    public float? PresencePenalty { get; set; }

    /// <summary>
    /// A list of tools the model may call
    /// </summary>
    public List<ChatToolDto>? Tools { get; set; }

    /// <summary>
    /// Controls which (if any) tool is called by the model
    /// </summary>
    public object? ToolChoice { get; set; }

    /// <summary>
    /// A unique identifier representing your end-user
    /// </summary>
    [StringLength(256)]
    public string? User { get; set; }

    /// <summary>
    /// Optional title for the chat (used when creating a new chat)
    /// </summary>
    [StringLength(500)]
    public string? Title { get; set; }

    /// <summary>
    /// Optional metadata for the chat
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// DTO for chat messages in requests
/// </summary>
public class ChatMessageDto
{
    /// <summary>
    /// The role of the message author (system, user, assistant, tool)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// The contents of the message
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// An optional name for the participant
    /// </summary>
    [StringLength(256)]
    public string? Name { get; set; }

    /// <summary>
    /// The tool calls generated by the model
    /// </summary>
    public List<ChatToolCallDto>? ToolCalls { get; set; }

    /// <summary>
    /// Tool call that this message is responding to
    /// </summary>
    [StringLength(256)]
    public string? ToolCallId { get; set; }
}

/// <summary>
/// DTO for tool definitions
/// </summary>
public class ChatToolDto
{
    /// <summary>
    /// The type of the tool (currently only "function" is supported)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Type { get; set; } = "function";

    /// <summary>
    /// The function definition
    /// </summary>
    [Required]
    public ChatFunctionDto Function { get; set; } = new();
}

/// <summary>
/// DTO for function definitions
/// </summary>
public class ChatFunctionDto
{
    /// <summary>
    /// The name of the function to be called
    /// </summary>
    [Required]
    [StringLength(256)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// A description of what the function does
    /// </summary>
    [StringLength(2000)]
    public string? Description { get; set; }

    /// <summary>
    /// The parameters the function accepts, described as a JSON Schema object
    /// </summary>
    public object? Parameters { get; set; }
}

/// <summary>
/// DTO for tool calls
/// </summary>
public class ChatToolCallDto
{
    /// <summary>
    /// The ID of the tool call
    /// </summary>
    [Required]
    [StringLength(256)]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// The type of the tool (currently only "function" is supported)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Type { get; set; } = "function";

    /// <summary>
    /// The function that the model called
    /// </summary>
    [Required]
    public ChatFunctionCallDto Function { get; set; } = new();
}

/// <summary>
/// DTO for function calls
/// </summary>
public class ChatFunctionCallDto
{
    /// <summary>
    /// The name of the function to call
    /// </summary>
    [Required]
    [StringLength(256)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// The arguments to call the function with, as generated by the model in JSON format
    /// </summary>
    public string? Arguments { get; set; }
}
