namespace VeasyApi.Models;

/// <summary>
/// Response model for chat completion API
/// </summary>
public class ChatCompletionResponse
{
    /// <summary>
    /// Unique identifier for the chat completion
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// The object type, which is always "chat.completion"
    /// </summary>
    public string Object { get; set; } = "chat.completion";

    /// <summary>
    /// The Unix timestamp (in seconds) of when the chat completion was created
    /// </summary>
    public long Created { get; set; }

    /// <summary>
    /// The model used for the chat completion
    /// </summary>
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// The chat ID (if continuing an existing conversation)
    /// </summary>
    public Guid? ChatId { get; set; }

    /// <summary>
    /// A list of chat completion choices
    /// </summary>
    public List<ChatChoiceDto> Choices { get; set; } = new();

    /// <summary>
    /// Usage statistics for the completion request
    /// </summary>
    public ChatUsageDto? Usage { get; set; }

    /// <summary>
    /// System fingerprint
    /// </summary>
    public string? SystemFingerprint { get; set; }

    /// <summary>
    /// Indicates whether the response was successful
    /// </summary>
    public bool IsSuccessful { get; set; } = true;

    /// <summary>
    /// Error message if the request failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Timestamp when the response was generated
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// DTO for chat completion choices
/// </summary>
public class ChatChoiceDto
{
    /// <summary>
    /// The index of the choice in the list of choices
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// A chat completion message generated by the model
    /// </summary>
    public ChatMessageResponseDto Message { get; set; } = new();

    /// <summary>
    /// Log probability information for the choice
    /// </summary>
    public object? Logprobs { get; set; }

    /// <summary>
    /// The reason the model stopped generating tokens
    /// </summary>
    public string? FinishReason { get; set; }
}

/// <summary>
/// DTO for chat messages in responses
/// </summary>
public class ChatMessageResponseDto
{
    /// <summary>
    /// The role of the author of this message
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// The contents of the message
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// The tool calls generated by the model
    /// </summary>
    public List<ChatToolCallDto>? ToolCalls { get; set; }

    /// <summary>
    /// The refusal message generated by the model
    /// </summary>
    public string? Refusal { get; set; }
}

/// <summary>
/// DTO for usage statistics
/// </summary>
public class ChatUsageDto
{
    /// <summary>
    /// Number of tokens in the prompt
    /// </summary>
    public int PromptTokens { get; set; }

    /// <summary>
    /// Number of tokens in the generated completion
    /// </summary>
    public int CompletionTokens { get; set; }

    /// <summary>
    /// Total number of tokens used in the request (prompt + completion)
    /// </summary>
    public int TotalTokens { get; set; }

    /// <summary>
    /// Breakdown of completion tokens usage
    /// </summary>
    public ChatCompletionTokensDetailsDto? CompletionTokensDetails { get; set; }

    /// <summary>
    /// Breakdown of prompt tokens usage
    /// </summary>
    public ChatPromptTokensDetailsDto? PromptTokensDetails { get; set; }
}

/// <summary>
/// DTO for completion tokens details
/// </summary>
public class ChatCompletionTokensDetailsDto
{
    /// <summary>
    /// Tokens generated by reasoning
    /// </summary>
    public int? ReasoningTokens { get; set; }

    /// <summary>
    /// Tokens generated by audio
    /// </summary>
    public int? AudioTokens { get; set; }

    /// <summary>
    /// Tokens generated by accepted prediction
    /// </summary>
    public int? AcceptedPredictionTokens { get; set; }

    /// <summary>
    /// Tokens generated by rejected prediction
    /// </summary>
    public int? RejectedPredictionTokens { get; set; }
}

/// <summary>
/// DTO for prompt tokens details
/// </summary>
public class ChatPromptTokensDetailsDto
{
    /// <summary>
    /// Tokens from cached prompt
    /// </summary>
    public int? CachedTokens { get; set; }

    /// <summary>
    /// Tokens from audio
    /// </summary>
    public int? AudioTokens { get; set; }
}
