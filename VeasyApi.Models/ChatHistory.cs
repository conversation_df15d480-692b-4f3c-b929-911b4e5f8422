using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VeasyApi.Models;

/// <summary>
/// Represents a chat conversation history entity
/// </summary>
[Table("ChatHistories")]
public class ChatHistory
{
    /// <summary>
    /// Unique identifier for the chat conversation
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid ChatId { get; set; }

    /// <summary>
    /// Identifier of the user who owns this chat
    /// </summary>
    [Required]
    [StringLength(256)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Title or name of the chat conversation
    /// </summary>
    [Required]
    [StringLength(500, MinimumLength = 1)]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Optional description or summary of the chat
    /// </summary>
    [StringLength(2000)]
    public string? Description { get; set; }

    /// <summary>
    /// Timestamp when the chat was created
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Timestamp when the chat was last updated
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Indicates whether the chat is active/available
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Total number of messages in this chat
    /// </summary>
    public int MessageCount { get; set; } = 0;

    /// <summary>
    /// Total token count for all messages in this chat
    /// </summary>
    public int TotalTokenCount { get; set; } = 0;

    /// <summary>
    /// Optional metadata associated with the chat
    /// </summary>
    [Column(TypeName = "jsonb")]
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Navigation property for the messages in this chat
    /// </summary>
    public virtual ICollection<ChatMessage> Messages { get; set; } = new List<ChatMessage>();

    /// <summary>
    /// Updates the UpdatedAt timestamp to the current UTC time
    /// </summary>
    public void UpdateTimestamp()
    {
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Increments the message count and updates the total token count
    /// </summary>
    /// <param name="tokenCount">Number of tokens to add</param>
    public void AddMessage(int tokenCount = 0)
    {
        MessageCount++;
        TotalTokenCount += tokenCount;
        UpdateTimestamp();
    }

    /// <summary>
    /// Decrements the message count and updates the total token count
    /// </summary>
    /// <param name="tokenCount">Number of tokens to subtract</param>
    public void RemoveMessage(int tokenCount = 0)
    {
        MessageCount = Math.Max(0, MessageCount - 1);
        TotalTokenCount = Math.Max(0, TotalTokenCount - tokenCount);
        UpdateTimestamp();
    }
}
