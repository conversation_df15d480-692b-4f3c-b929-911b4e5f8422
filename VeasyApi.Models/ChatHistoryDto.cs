using System.ComponentModel.DataAnnotations;

namespace VeasyApi.Models;

/// <summary>
/// DTO for chat history responses
/// </summary>
public class ChatHistoryDto
{
    /// <summary>
    /// Unique identifier for the chat conversation
    /// </summary>
    public Guid ChatId { get; set; }

    /// <summary>
    /// Identifier of the user who owns this chat
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Title or name of the chat conversation
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Optional description or summary of the chat
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Timestamp when the chat was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Timestamp when the chat was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Indicates whether the chat is active/available
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Total number of messages in this chat
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// Total token count for all messages in this chat
    /// </summary>
    public int TotalTokenCount { get; set; }

    /// <summary>
    /// Optional metadata associated with the chat
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Messages in this chat (optional, included when requested)
    /// </summary>
    public List<ChatMessageDto>? Messages { get; set; }
}

/// <summary>
/// DTO for paginated chat history responses
/// </summary>
public class ChatHistoryPagedResponse
{
    /// <summary>
    /// List of chat histories
    /// </summary>
    public List<ChatHistoryDto> Chats { get; set; } = new();

    /// <summary>
    /// Current page number (1-based)
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of chat histories
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Indicates if there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Indicates if there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }
}

/// <summary>
/// Request model for getting chat history with pagination
/// </summary>
public class ChatHistoryRequest
{
    /// <summary>
    /// Page number (1-based, default: 1)
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "Page must be greater than 0")]
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of items per page (default: 20, max: 100)
    /// </summary>
    [Range(1, 100, ErrorMessage = "PageSize must be between 1 and 100")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Optional search term to filter chat titles
    /// </summary>
    [StringLength(500)]
    public string? Search { get; set; }

    /// <summary>
    /// Optional filter to include only active chats
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Optional date filter - only chats created after this date
    /// </summary>
    public DateTime? CreatedAfter { get; set; }

    /// <summary>
    /// Optional date filter - only chats created before this date
    /// </summary>
    public DateTime? CreatedBefore { get; set; }

    /// <summary>
    /// Sort field (CreatedAt, UpdatedAt, Title, MessageCount)
    /// </summary>
    [StringLength(50)]
    public string SortBy { get; set; } = "UpdatedAt";

    /// <summary>
    /// Sort direction (asc, desc)
    /// </summary>
    [StringLength(10)]
    public string SortDirection { get; set; } = "desc";

    /// <summary>
    /// Whether to include message count in the response
    /// </summary>
    public bool IncludeMessages { get; set; } = false;
}

/// <summary>
/// DTO for detailed chat message responses
/// </summary>
public class ChatMessageDetailDto
{
    /// <summary>
    /// Unique identifier for the message
    /// </summary>
    public Guid MessageId { get; set; }

    /// <summary>
    /// The role of the message sender
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// The content/text of the message
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when the message was created
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Number of tokens in this message
    /// </summary>
    public int TokenCount { get; set; }

    /// <summary>
    /// Optional name/identifier for the message sender
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Optional function call information
    /// </summary>
    public Dictionary<string, object>? FunctionCall { get; set; }

    /// <summary>
    /// Optional tool calls information
    /// </summary>
    public List<Dictionary<string, object>>? ToolCalls { get; set; }

    /// <summary>
    /// Optional tool call ID
    /// </summary>
    public string? ToolCallId { get; set; }

    /// <summary>
    /// Optional metadata associated with the message
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Order/sequence number of the message within the chat
    /// </summary>
    public int SequenceNumber { get; set; }
}
