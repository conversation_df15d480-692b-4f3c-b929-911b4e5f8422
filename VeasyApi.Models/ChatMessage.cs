using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VeasyApi.Models;

/// <summary>
/// Represents a message within a chat conversation
/// </summary>
[Table("ChatMessages")]
public class ChatMessage
{
    /// <summary>
    /// Unique identifier for the message
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid MessageId { get; set; }

    /// <summary>
    /// Foreign key reference to the chat this message belongs to
    /// </summary>
    [Required]
    public Guid ChatId { get; set; }

    /// <summary>
    /// The role of the message sender (user, assistant, system, tool)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// The content/text of the message
    /// </summary>
    [Required]
    [Column(TypeName = "text")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when the message was created
    /// </summary>
    [Required]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Number of tokens in this message (for billing/usage tracking)
    /// </summary>
    public int TokenCount { get; set; } = 0;

    /// <summary>
    /// Optional name/identifier for the message sender
    /// </summary>
    [StringLength(256)]
    public string? Name { get; set; }

    /// <summary>
    /// Optional function call information (for tool/function calls)
    /// </summary>
    [Column(TypeName = "jsonb")]
    public Dictionary<string, object>? FunctionCall { get; set; }

    /// <summary>
    /// Optional tool calls information (for multiple tool calls)
    /// </summary>
    [Column(TypeName = "jsonb")]
    public List<Dictionary<string, object>>? ToolCalls { get; set; }

    /// <summary>
    /// Optional tool call ID (for tool responses)
    /// </summary>
    [StringLength(256)]
    public string? ToolCallId { get; set; }

    /// <summary>
    /// Optional metadata associated with the message
    /// </summary>
    [Column(TypeName = "jsonb")]
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Indicates whether the message is active/visible
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Order/sequence number of the message within the chat
    /// </summary>
    public int SequenceNumber { get; set; } = 0;

    /// <summary>
    /// Navigation property back to the chat this message belongs to
    /// </summary>
    [ForeignKey(nameof(ChatId))]
    public virtual ChatHistory Chat { get; set; } = null!;

    /// <summary>
    /// Checks if this message is from a user
    /// </summary>
    public bool IsUserMessage => Role.Equals("user", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Checks if this message is from an assistant
    /// </summary>
    public bool IsAssistantMessage => Role.Equals("assistant", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Checks if this message is a system message
    /// </summary>
    public bool IsSystemMessage => Role.Equals("system", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Checks if this message is a tool/function call
    /// </summary>
    public bool IsToolMessage => Role.Equals("tool", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Gets the content length in characters
    /// </summary>
    public int ContentLength => Content?.Length ?? 0;
}
