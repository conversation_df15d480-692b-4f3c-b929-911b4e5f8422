using System.ComponentModel.DataAnnotations;

namespace VeasyApi.Models;

/// <summary>
/// Represents a document entity for RAG operations
/// </summary>
public class Document
{
    /// <summary>
    /// Unique identifier for the document
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// The title or name of the document
    /// </summary>
    [Required]
    [StringLength(500, MinimumLength = 1)]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// The main content/text of the document
    /// </summary>
    [Required]
    [StringLength(50000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Optional metadata associated with the document
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// The source or origin of the document (e.g., file path, URL)
    /// </summary>
    public string? Source { get; set; }

    /// <summary>
    /// The MIME type or content type of the document
    /// </summary>
    public string? ContentType { get; set; }

    /// <summary>
    /// The timestamp when the document was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// The timestamp when the document was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Optional tags for categorizing the document
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// The size of the document content in characters
    /// </summary>
    public int ContentLength => Content?.Length ?? 0;

    /// <summary>
    /// Indicates whether the document is active/available
    /// </summary>
    public bool IsActive { get; set; } = true;
}