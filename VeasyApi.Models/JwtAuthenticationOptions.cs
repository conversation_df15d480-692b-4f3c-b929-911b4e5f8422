namespace VeasyApi.Models;

/// <summary>
/// Configuration options for JWT Bearer authentication
/// </summary>
public class JwtAuthenticationOptions
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "Authentication:Jwt";

    /// <summary>
    /// The authority (Identity Server URL) that issued the token
    /// </summary>
    public string Authority { get; set; } = string.Empty;

    /// <summary>
    /// The audience that this API represents
    /// </summary>
    public string Audience { get; set; } = string.Empty;

    /// <summary>
    /// Whether to require HTTPS for the authority
    /// </summary>
    public bool RequireHttpsMetadata { get; set; } = true;

    /// <summary>
    /// Whether to validate the issuer
    /// </summary>
    public bool ValidateIssuer { get; set; } = true;

    /// <summary>
    /// Whether to validate the audience
    /// </summary>
    public bool ValidateAudience { get; set; } = true;

    /// <summary>
    /// Whether to validate the token lifetime
    /// </summary>
    public bool ValidateLifetime { get; set; } = true;

    /// <summary>
    /// Whether to validate the issuer signing key
    /// </summary>
    public bool ValidateIssuerSigningKey { get; set; } = true;

    /// <summary>
    /// Clock skew tolerance in minutes
    /// </summary>
    public int ClockSkewMinutes { get; set; } = 5;

    /// <summary>
    /// Whether to save the token in the authentication properties
    /// </summary>
    public bool SaveToken { get; set; } = true;

    /// <summary>
    /// The name of the claim that contains the user ID
    /// </summary>
    public string UserIdClaimType { get; set; } = "sub";

    /// <summary>
    /// The name of the claim that contains the user name
    /// </summary>
    public string UserNameClaimType { get; set; } = "name";

    /// <summary>
    /// The name of the claim that contains the user email
    /// </summary>
    public string EmailClaimType { get; set; } = "email";

    /// <summary>
    /// The name of the claim that contains the user roles
    /// </summary>
    public string RoleClaimType { get; set; } = "role";

    /// <summary>
    /// Additional scopes required for this API
    /// </summary>
    public List<string> RequiredScopes { get; set; } = new();

    /// <summary>
    /// Whether authentication is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Paths that should be excluded from authentication
    /// </summary>
    public List<string> ExcludedPaths { get; set; } = new()
    {
        "/health",
        "/swagger",
        "/scalar",
        "/.well-known"
    };

    /// <summary>
    /// Custom claim mappings (from token claim to application claim)
    /// </summary>
    public Dictionary<string, string> ClaimMappings { get; set; } = new();
}
