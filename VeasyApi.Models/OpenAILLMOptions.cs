namespace VeasyApi.Models;

/// <summary>
/// Configuration options for OpenAI LLM services
/// </summary>
public class OpenAILLMOptions
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "OpenAI:LLM";

    /// <summary>
    /// The OpenAI model name to use for chat completions
    /// </summary>
    public string ModelName { get; set; } = "gpt-4o-mini";

    /// <summary>
    /// Maximum context length supported by the model
    /// </summary>
    public int MaxContextLength { get; set; } = 128000;

    /// <summary>
    /// Default maximum tokens for completions
    /// </summary>
    public int DefaultMaxTokens { get; set; } = 1000;

    /// <summary>
    /// Default temperature for completions
    /// </summary>
    public float DefaultTemperature { get; set; } = 0.7f;

    /// <summary>
    /// Default top-p value for completions
    /// </summary>
    public float DefaultTopP { get; set; } = 1.0f;

    /// <summary>
    /// Default frequency penalty for completions
    /// </summary>
    public float DefaultFrequencyPenalty { get; set; } = 0.0f;

    /// <summary>
    /// Default presence penalty for completions
    /// </summary>
    public float DefaultPresencePenalty { get; set; } = 0.0f;

    /// <summary>
    /// Whether to enable function calling by default
    /// </summary>
    public bool EnableFunctionCalling { get; set; } = true;

    /// <summary>
    /// Whether to enable streaming responses by default
    /// </summary>
    public bool EnableStreaming { get; set; } = true;

    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 120;

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay for exponential backoff in milliseconds
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;
}
