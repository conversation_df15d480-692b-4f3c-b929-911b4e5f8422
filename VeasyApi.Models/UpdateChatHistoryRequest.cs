using System.ComponentModel.DataAnnotations;

namespace VeasyApi.Models;

/// <summary>
/// Request model for updating chat history
/// </summary>
public class UpdateChatHistoryRequest
{
    /// <summary>
    /// New title for the chat (optional)
    /// </summary>
    [StringLength(500, MinimumLength = 1)]
    public string? Title { get; set; }

    /// <summary>
    /// New description for the chat (optional)
    /// </summary>
    [StringLength(2000)]
    public string? Description { get; set; }

    /// <summary>
    /// New metadata for the chat (optional)
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}
