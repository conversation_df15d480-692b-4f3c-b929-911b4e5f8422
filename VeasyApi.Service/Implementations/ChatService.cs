using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OpenAI;
using OpenAI.Chat;
using System.Diagnostics;
using VeasyApi.Domain.Abstractions;
using VeasyApi.Models;
using VeasyApiDotNet.Data;

namespace VeasyApi.Service.Implementations;

/// <summary>
/// Service for chat completion and history management
/// </summary>
public class ChatService : IChatService
{
    private readonly ChatDbContext _dbContext;
    private readonly OpenAIClient _openAIClient;
    private readonly ILogger<ChatService> _logger;
    private readonly OpenAILLMOptions _llmOptions;

    public ChatService(
        ChatDbContext dbContext,
        OpenAIClient openAIClient,
        ILogger<ChatService> logger,
        IOptions<OpenAILLMOptions> llmOptions)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _openAIClient = openAIClient ?? throw new ArgumentNullException(nameof(openAIClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _llmOptions = llmOptions?.Value ?? throw new ArgumentNullException(nameof(llmOptions));
    }

    public async Task<ChatCompletionResponse> CreateChatCompletionAsync(
        ChatCompletionRequest request, 
        string userId, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Creating chat completion for user {UserId}", userId);

            // Validate request
            var validationErrors = ValidateRequest(request);
            if (validationErrors.Any())
            {
                return new ChatCompletionResponse
                {
                    IsSuccessful = false,
                    ErrorMessage = string.Join("; ", validationErrors),
                    ProcessingTimeMs = stopwatch.ElapsedMilliseconds
                };
            }

            // Get or create chat history
            ChatHistory? chatHistory = null;
            if (request.ChatId.HasValue)
            {
                chatHistory = await _dbContext.ChatHistories
                    .FirstOrDefaultAsync(c => c.ChatId == request.ChatId.Value && c.UserId == userId, cancellationToken);
                
                if (chatHistory == null)
                {
                    _logger.LogWarning("Chat {ChatId} not found for user {UserId}", request.ChatId.Value, userId);
                    return new ChatCompletionResponse
                    {
                        IsSuccessful = false,
                        ErrorMessage = "Chat not found",
                        ProcessingTimeMs = stopwatch.ElapsedMilliseconds
                    };
                }
            }

            // Convert messages to OpenAI format
            var openAIMessages = ConvertToOpenAIMessages(request.Messages);

            // Create OpenAI chat completion request
            var chatRequest = new ChatCompletionOptions
            {
                Model = request.Model ?? _llmOptions.ModelName,
                Messages = openAIMessages,
                MaxTokens = request.MaxTokens,
                Temperature = request.Temperature,
                TopP = request.TopP,
                FrequencyPenalty = request.FrequencyPenalty,
                PresencePenalty = request.PresencePenalty,
                Stop = request.Stop,
                User = request.User
            };

            // Add tools if specified
            if (request.Tools?.Any() == true)
            {
                foreach (var tool in request.Tools)
                {
                    chatRequest.Tools.Add(ConvertToOpenAITool(tool));
                }
                
                if (request.ToolChoice != null)
                {
                    // Handle tool choice conversion based on type
                    chatRequest.ToolChoice = ConvertToolChoice(request.ToolChoice);
                }
            }

            // Call OpenAI API
            var response = await _openAIClient.GetChatClient(chatRequest.Model).CompleteChatAsync(chatRequest, cancellationToken);

            stopwatch.Stop();

            // Create or update chat history
            if (chatHistory == null && (request.ChatId.HasValue || !string.IsNullOrEmpty(request.Title)))
            {
                chatHistory = new ChatHistory
                {
                    ChatId = request.ChatId ?? Guid.NewGuid(),
                    UserId = userId,
                    Title = request.Title ?? GenerateChatTitle(request.Messages.FirstOrDefault()?.Content ?? "New Chat"),
                    Description = null,
                    Metadata = request.Metadata ?? new Dictionary<string, object>()
                };

                _dbContext.ChatHistories.Add(chatHistory);
            }

            // Save messages to database if we have a chat history
            if (chatHistory != null)
            {
                await SaveMessagesToDatabase(chatHistory, request.Messages, response, cancellationToken);
            }

            // Convert response
            var chatResponse = ConvertFromOpenAIResponse(response, chatHistory?.ChatId, stopwatch.ElapsedMilliseconds);

            _logger.LogInformation("Chat completion created successfully in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

            return chatResponse;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error creating chat completion for user {UserId}", userId);
            
            return new ChatCompletionResponse
            {
                IsSuccessful = false,
                ErrorMessage = ex.Message,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
    }

    public async IAsyncEnumerable<string> CreateStreamingChatCompletionAsync(
        ChatCompletionRequest request, 
        string userId, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating streaming chat completion for user {UserId}", userId);

        // Validate request
        var validationErrors = ValidateRequest(request);
        if (validationErrors.Any())
        {
            yield return $"data: {{\"error\": \"{string.Join("; ", validationErrors)}\"}}\n\n";
            yield break;
        }

        // Get or create chat history
        ChatHistory? chatHistory = null;
        if (request.ChatId.HasValue)
        {
            chatHistory = await _dbContext.ChatHistories
                .FirstOrDefaultAsync(c => c.ChatId == request.ChatId.Value && c.UserId == userId, cancellationToken);
        }

        try
        {
            // Convert messages to OpenAI format
            var openAIMessages = ConvertToOpenAIMessages(request.Messages);

            // Create OpenAI streaming chat completion request
            var chatRequest = new ChatCompletionOptions
            {
                Model = request.Model ?? _llmOptions.ModelName,
                Messages = openAIMessages,
                MaxTokens = request.MaxTokens,
                Temperature = request.Temperature,
                TopP = request.TopP,
                FrequencyPenalty = request.FrequencyPenalty,
                PresencePenalty = request.PresencePenalty,
                Stop = request.Stop,
                User = request.User
            };

            // Add tools if specified
            if (request.Tools?.Any() == true)
            {
                foreach (var tool in request.Tools)
                {
                    chatRequest.Tools.Add(ConvertToOpenAITool(tool));
                }
                
                if (request.ToolChoice != null)
                {
                    chatRequest.ToolChoice = ConvertToolChoice(request.ToolChoice);
                }
            }

            // Stream response from OpenAI
            var streamingResponse = _openAIClient.GetChatClient(chatRequest.Model).CompleteChatStreamingAsync(chatRequest, cancellationToken);

            await foreach (var update in streamingResponse)
            {
                if (cancellationToken.IsCancellationRequested)
                    yield break;

                // Convert streaming update to SSE format
                var chunk = ConvertStreamingUpdate(update, chatHistory?.ChatId);
                if (!string.IsNullOrEmpty(chunk))
                {
                    yield return chunk;
                }
            }

            // Send completion signal
            yield return "data: [DONE]\n\n";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in streaming chat completion for user {UserId}", userId);
            yield return $"data: {{\"error\": \"{ex.Message}\"}}\n\n";
        }
    }

    public async Task<ChatHistoryDto?> GetChatHistoryAsync(
        Guid chatId, 
        string userId, 
        bool includeMessages = true, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting chat history {ChatId} for user {UserId}", chatId, userId);

        var query = _dbContext.ChatHistories
            .Where(c => c.ChatId == chatId && c.UserId == userId);

        if (includeMessages)
        {
            query = query.Include(c => c.Messages.OrderBy(m => m.SequenceNumber));
        }

        var chatHistory = await query.FirstOrDefaultAsync(cancellationToken);

        if (chatHistory == null)
        {
            _logger.LogWarning("Chat history {ChatId} not found for user {UserId}", chatId, userId);
            return null;
        }

        return ConvertToDto(chatHistory, includeMessages);
    }

    public async Task<ChatHistoryPagedResponse> GetChatHistoriesAsync(
        string userId,
        ChatHistoryRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting chat histories for user {UserId}, page {Page}", userId, request.Page);

        var query = _dbContext.ChatHistories
            .Where(c => c.UserId == userId);

        // Apply filters
        if (request.IsActive.HasValue)
        {
            query = query.Where(c => c.IsActive == request.IsActive.Value);
        }

        if (!string.IsNullOrEmpty(request.Search))
        {
            query = query.Where(c => c.Title.Contains(request.Search) ||
                                   (c.Description != null && c.Description.Contains(request.Search)));
        }

        if (request.CreatedAfter.HasValue)
        {
            query = query.Where(c => c.CreatedAt >= request.CreatedAfter.Value);
        }

        if (request.CreatedBefore.HasValue)
        {
            query = query.Where(c => c.CreatedAt <= request.CreatedBefore.Value);
        }

        // Apply sorting
        query = request.SortBy.ToLower() switch
        {
            "createdat" => request.SortDirection.ToLower() == "asc"
                ? query.OrderBy(c => c.CreatedAt)
                : query.OrderByDescending(c => c.CreatedAt),
            "title" => request.SortDirection.ToLower() == "asc"
                ? query.OrderBy(c => c.Title)
                : query.OrderByDescending(c => c.Title),
            "messagecount" => request.SortDirection.ToLower() == "asc"
                ? query.OrderBy(c => c.MessageCount)
                : query.OrderByDescending(c => c.MessageCount),
            _ => request.SortDirection.ToLower() == "asc"
                ? query.OrderBy(c => c.UpdatedAt)
                : query.OrderByDescending(c => c.UpdatedAt)
        };

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var chats = await query
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Include messages if requested
        if (request.IncludeMessages)
        {
            var chatIds = chats.Select(c => c.ChatId).ToList();
            var messages = await _dbContext.ChatMessages
                .Where(m => chatIds.Contains(m.ChatId))
                .OrderBy(m => m.SequenceNumber)
                .ToListAsync(cancellationToken);

            foreach (var chat in chats)
            {
                chat.Messages = messages.Where(m => m.ChatId == chat.ChatId).ToList();
            }
        }

        var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

        return new ChatHistoryPagedResponse
        {
            Chats = chats.Select(c => ConvertToDto(c, request.IncludeMessages)).ToList(),
            Page = request.Page,
            PageSize = request.PageSize,
            TotalCount = totalCount,
            TotalPages = totalPages,
            HasNextPage = request.Page < totalPages,
            HasPreviousPage = request.Page > 1
        };
    }

    public async Task<ChatHistoryDto?> UpdateChatHistoryAsync(
        Guid chatId,
        string userId,
        string? title = null,
        string? description = null,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Updating chat history {ChatId} for user {UserId}", chatId, userId);

        var chatHistory = await _dbContext.ChatHistories
            .FirstOrDefaultAsync(c => c.ChatId == chatId && c.UserId == userId, cancellationToken);

        if (chatHistory == null)
        {
            _logger.LogWarning("Chat history {ChatId} not found for user {UserId}", chatId, userId);
            return null;
        }

        // Update properties
        if (!string.IsNullOrEmpty(title))
        {
            chatHistory.Title = title;
        }

        if (description != null)
        {
            chatHistory.Description = description;
        }

        if (metadata != null)
        {
            chatHistory.Metadata = metadata;
        }

        chatHistory.UpdateTimestamp();

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated chat history {ChatId} for user {UserId}", chatId, userId);

        return ConvertToDto(chatHistory, false);
    }

    public async Task<bool> DeleteChatHistoryAsync(
        Guid chatId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Deleting chat history {ChatId} for user {UserId}", chatId, userId);

        var chatHistory = await _dbContext.ChatHistories
            .FirstOrDefaultAsync(c => c.ChatId == chatId && c.UserId == userId, cancellationToken);

        if (chatHistory == null)
        {
            _logger.LogWarning("Chat history {ChatId} not found for user {UserId}", chatId, userId);
            return false;
        }

        _dbContext.ChatHistories.Remove(chatHistory);
        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Deleted chat history {ChatId} for user {UserId}", chatId, userId);

        return true;
    }

    public async Task<bool> ArchiveChatHistoryAsync(
        Guid chatId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Archiving chat history {ChatId} for user {UserId}", chatId, userId);

        var chatHistory = await _dbContext.ChatHistories
            .FirstOrDefaultAsync(c => c.ChatId == chatId && c.UserId == userId, cancellationToken);

        if (chatHistory == null)
        {
            _logger.LogWarning("Chat history {ChatId} not found for user {UserId}", chatId, userId);
            return false;
        }

        chatHistory.IsActive = false;
        chatHistory.UpdateTimestamp();

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Archived chat history {ChatId} for user {UserId}", chatId, userId);

        return true;
    }

    public async Task<List<ChatMessageDetailDto>> GetChatMessagesAsync(
        Guid chatId,
        string userId,
        int page = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting messages for chat {ChatId}, user {UserId}, page {Page}", chatId, userId, page);

        // Verify user owns the chat
        var chatExists = await _dbContext.ChatHistories
            .AnyAsync(c => c.ChatId == chatId && c.UserId == userId, cancellationToken);

        if (!chatExists)
        {
            _logger.LogWarning("Chat {ChatId} not found for user {UserId}", chatId, userId);
            return new List<ChatMessageDetailDto>();
        }

        var messages = await _dbContext.ChatMessages
            .Where(m => m.ChatId == chatId && m.IsActive)
            .OrderBy(m => m.SequenceNumber)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return messages.Select(ConvertToDetailDto).ToList();
    }

    public int EstimateTokenCount(List<ChatMessageDto> messages, string? model = null)
    {
        // Simple token estimation - approximately 4 characters per token
        // This is a rough estimate; for precise counting, you'd use a tokenizer library
        var totalCharacters = messages.Sum(m => m.Content?.Length ?? 0);
        var estimatedTokens = (int)Math.Ceiling(totalCharacters / 4.0);

        // Add some overhead for message structure and metadata
        var overhead = messages.Count * 10; // ~10 tokens per message for structure

        return estimatedTokens + overhead;
    }

    public List<string> ValidateRequest(ChatCompletionRequest request)
    {
        var errors = new List<string>();

        if (request.Messages == null || !request.Messages.Any())
        {
            errors.Add("At least one message is required");
        }
        else
        {
            for (int i = 0; i < request.Messages.Count; i++)
            {
                var message = request.Messages[i];
                if (string.IsNullOrEmpty(message.Role))
                {
                    errors.Add($"Message {i + 1}: Role is required");
                }
                if (string.IsNullOrEmpty(message.Content) && message.ToolCalls?.Any() != true)
                {
                    errors.Add($"Message {i + 1}: Content is required when no tool calls are present");
                }
            }
        }

        if (request.MaxTokens.HasValue && (request.MaxTokens < 1 || request.MaxTokens > 32000))
        {
            errors.Add("MaxTokens must be between 1 and 32000");
        }

        if (request.Temperature.HasValue && (request.Temperature < 0 || request.Temperature > 2))
        {
            errors.Add("Temperature must be between 0.0 and 2.0");
        }

        if (request.TopP.HasValue && (request.TopP < 0 || request.TopP > 1))
        {
            errors.Add("TopP must be between 0.0 and 1.0");
        }

        return errors;
    }

    #region Helper Methods

    private static List<ChatMessage> ConvertToOpenAIMessages(List<ChatMessageDto> messages)
    {
        var openAIMessages = new List<ChatMessage>();

        foreach (var message in messages)
        {
            var openAIMessage = new UserChatMessage(message.Content);

            // Set role-specific message types
            switch (message.Role.ToLower())
            {
                case "system":
                    openAIMessage = new SystemChatMessage(message.Content);
                    break;
                case "user":
                    openAIMessage = new UserChatMessage(message.Content);
                    break;
                case "assistant":
                    var assistantMessage = new AssistantChatMessage(message.Content);
                    if (message.ToolCalls?.Any() == true)
                    {
                        foreach (var toolCall in message.ToolCalls)
                        {
                            assistantMessage.ToolCalls.Add(new ChatToolCall(
                                toolCall.Id,
                                toolCall.Function.Name,
                                BinaryData.FromString(toolCall.Function.Arguments ?? "{}")
                            ));
                        }
                    }
                    openAIMessage = assistantMessage;
                    break;
                case "tool":
                    openAIMessage = new ToolChatMessage(message.ToolCallId ?? "", message.Content);
                    break;
            }

            openAIMessages.Add(openAIMessage);
        }

        return openAIMessages;
    }

    private static ChatTool ConvertToOpenAITool(ChatToolDto tool)
    {
        var function = ChatFunction.CreateFunctionTool(
            tool.Function.Name,
            tool.Function.Description,
            BinaryData.FromString(System.Text.Json.JsonSerializer.Serialize(tool.Function.Parameters))
        );

        return function;
    }

    private static ChatToolChoice ConvertToolChoice(object toolChoice)
    {
        // Handle different tool choice formats
        if (toolChoice is string str)
        {
            return str.ToLower() switch
            {
                "none" => ChatToolChoice.CreateNoneChoice(),
                "auto" => ChatToolChoice.CreateAutoChoice(),
                "required" => ChatToolChoice.CreateRequiredChoice(),
                _ => ChatToolChoice.CreateAutoChoice()
            };
        }

        // For more complex tool choice objects, you might need additional parsing
        return ChatToolChoice.CreateAutoChoice();
    }

    private ChatCompletionResponse ConvertFromOpenAIResponse(ChatCompletion response, Guid? chatId, long processingTimeMs)
    {
        var choices = new List<ChatChoiceDto>();

        foreach (var choice in response.Choices)
        {
            var messageDto = new ChatMessageResponseDto
            {
                Role = choice.Message.Role.ToString().ToLower(),
                Content = choice.Message.Content?.FirstOrDefault()?.Text,
                Refusal = choice.Message.Refusal
            };

            // Handle tool calls
            if (choice.Message.ToolCalls?.Any() == true)
            {
                messageDto.ToolCalls = choice.Message.ToolCalls.Select(tc => new ChatToolCallDto
                {
                    Id = tc.Id,
                    Type = "function",
                    Function = new ChatFunctionCallDto
                    {
                        Name = tc.FunctionName,
                        Arguments = tc.FunctionArguments.ToString()
                    }
                }).ToList();
            }

            choices.Add(new ChatChoiceDto
            {
                Index = choice.Index,
                Message = messageDto,
                FinishReason = choice.FinishReason?.ToString()
            });
        }

        return new ChatCompletionResponse
        {
            Id = response.Id,
            Object = "chat.completion",
            Created = response.CreatedAt.ToUnixTimeSeconds(),
            Model = response.Model,
            ChatId = chatId,
            Choices = choices,
            Usage = response.Usage != null ? new ChatUsageDto
            {
                PromptTokens = response.Usage.InputTokens,
                CompletionTokens = response.Usage.OutputTokens,
                TotalTokens = response.Usage.TotalTokens
            } : null,
            SystemFingerprint = response.SystemFingerprint,
            ProcessingTimeMs = processingTimeMs,
            IsSuccessful = true
        };
    }

    private string ConvertStreamingUpdate(StreamingChatCompletionUpdate update, Guid? chatId)
    {
        // Convert streaming update to Server-Sent Events format
        var data = new
        {
            id = update.Id,
            @object = "chat.completion.chunk",
            created = update.CreatedAt.ToUnixTimeSeconds(),
            model = update.Model,
            chatId = chatId,
            choices = update.Choices.Select(choice => new
            {
                index = choice.Index,
                delta = new
                {
                    role = choice.Delta?.Role?.ToString()?.ToLower(),
                    content = choice.Delta?.Content?.FirstOrDefault()?.Text,
                    tool_calls = choice.Delta?.ToolCalls?.Select(tc => new
                    {
                        id = tc.Id,
                        type = "function",
                        function = new
                        {
                            name = tc.FunctionName,
                            arguments = tc.FunctionArguments?.ToString()
                        }
                    })
                },
                finish_reason = choice.FinishReason?.ToString()
            })
        };

        return $"data: {System.Text.Json.JsonSerializer.Serialize(data)}\n\n";
    }

    private async Task SaveMessagesToDatabase(
        ChatHistory chatHistory,
        List<ChatMessageDto> requestMessages,
        ChatCompletion response,
        CancellationToken cancellationToken)
    {
        var sequenceNumber = await _dbContext.ChatMessages
            .Where(m => m.ChatId == chatHistory.ChatId)
            .MaxAsync(m => (int?)m.SequenceNumber, cancellationToken) ?? 0;

        // Save request messages
        foreach (var message in requestMessages)
        {
            var chatMessage = new ChatMessage
            {
                ChatId = chatHistory.ChatId,
                Role = message.Role,
                Content = message.Content,
                Name = message.Name,
                ToolCallId = message.ToolCallId,
                SequenceNumber = ++sequenceNumber,
                TokenCount = EstimateTokenCount(new List<ChatMessageDto> { message })
            };

            if (message.ToolCalls?.Any() == true)
            {
                chatMessage.ToolCalls = message.ToolCalls.Select(tc => new Dictionary<string, object>
                {
                    ["id"] = tc.Id,
                    ["type"] = tc.Type,
                    ["function"] = new Dictionary<string, object>
                    {
                        ["name"] = tc.Function.Name,
                        ["arguments"] = tc.Function.Arguments ?? "{}"
                    }
                }).ToList();
            }

            _dbContext.ChatMessages.Add(chatMessage);
            chatHistory.AddMessage(chatMessage.TokenCount);
        }

        // Save response messages
        foreach (var choice in response.Choices)
        {
            var responseMessage = new ChatMessage
            {
                ChatId = chatHistory.ChatId,
                Role = "assistant",
                Content = choice.Message.Content?.FirstOrDefault()?.Text ?? "",
                SequenceNumber = ++sequenceNumber,
                TokenCount = response.Usage?.OutputTokens ?? 0
            };

            if (choice.Message.ToolCalls?.Any() == true)
            {
                responseMessage.ToolCalls = choice.Message.ToolCalls.Select(tc => new Dictionary<string, object>
                {
                    ["id"] = tc.Id,
                    ["type"] = "function",
                    ["function"] = new Dictionary<string, object>
                    {
                        ["name"] = tc.FunctionName,
                        ["arguments"] = tc.FunctionArguments.ToString()
                    }
                }).ToList();
            }

            _dbContext.ChatMessages.Add(responseMessage);
            chatHistory.AddMessage(responseMessage.TokenCount);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
    }

    private static string GenerateChatTitle(string firstMessage)
    {
        if (string.IsNullOrEmpty(firstMessage))
            return "New Chat";

        // Take first 50 characters and add ellipsis if longer
        var title = firstMessage.Length > 50 ? firstMessage[..47] + "..." : firstMessage;

        // Remove newlines and extra spaces
        title = System.Text.RegularExpressions.Regex.Replace(title, @"\s+", " ").Trim();

        return title;
    }

    private static ChatHistoryDto ConvertToDto(ChatHistory chatHistory, bool includeMessages)
    {
        var dto = new ChatHistoryDto
        {
            ChatId = chatHistory.ChatId,
            UserId = chatHistory.UserId,
            Title = chatHistory.Title,
            Description = chatHistory.Description,
            CreatedAt = chatHistory.CreatedAt,
            UpdatedAt = chatHistory.UpdatedAt,
            IsActive = chatHistory.IsActive,
            MessageCount = chatHistory.MessageCount,
            TotalTokenCount = chatHistory.TotalTokenCount,
            Metadata = chatHistory.Metadata
        };

        if (includeMessages && chatHistory.Messages?.Any() == true)
        {
            dto.Messages = chatHistory.Messages.Select(m => new ChatMessageDto
            {
                Role = m.Role,
                Content = m.Content,
                Name = m.Name,
                ToolCallId = m.ToolCallId,
                ToolCalls = m.ToolCalls?.Select(tc => new ChatToolCallDto
                {
                    Id = tc.GetValueOrDefault("id")?.ToString() ?? "",
                    Type = tc.GetValueOrDefault("type")?.ToString() ?? "function",
                    Function = new ChatFunctionCallDto
                    {
                        Name = ((Dictionary<string, object>?)tc.GetValueOrDefault("function"))?.GetValueOrDefault("name")?.ToString() ?? "",
                        Arguments = ((Dictionary<string, object>?)tc.GetValueOrDefault("function"))?.GetValueOrDefault("arguments")?.ToString()
                    }
                }).ToList()
            }).ToList();
        }

        return dto;
    }

    private static ChatMessageDetailDto ConvertToDetailDto(ChatMessage message)
    {
        return new ChatMessageDetailDto
        {
            MessageId = message.MessageId,
            Role = message.Role,
            Content = message.Content,
            Timestamp = message.Timestamp,
            TokenCount = message.TokenCount,
            Name = message.Name,
            FunctionCall = message.FunctionCall,
            ToolCalls = message.ToolCalls,
            ToolCallId = message.ToolCallId,
            Metadata = message.Metadata,
            SequenceNumber = message.SequenceNumber
        };
    }

    #endregion
}
