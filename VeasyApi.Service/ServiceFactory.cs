using VeasyApi.Domain.Abstractions;
using VeasyApi.Service.Implementations;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using OpenAI;

namespace VeasyApi.Service.Factories;

/// <summary>
/// Factory for creating service instances with proper configuration
/// </summary>
public class ServiceFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ServiceFactory> _logger;

    public ServiceFactory(IServiceProvider serviceProvider, ILogger<ServiceFactory> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Creates an embedding provider instance
    /// </summary>
    /// <param name="providerType">The type of embedding provider to create</param>
    /// <returns>An instance of the embedding provider</returns>
    public IEmbeddingProvider CreateEmbeddingProvider(EmbeddingProviderType providerType = EmbeddingProviderType.OpenAI)
    {
        try
        {
            return providerType switch
            {
                EmbeddingProviderType.OpenAI => _serviceProvider.GetRequiredService<OpenAIEmbeddingProvider>(),
                _ => throw new ArgumentException($"Unsupported embedding provider type: {providerType}", nameof(providerType))
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create embedding provider of type {ProviderType}", providerType);
            throw;
        }
    }

    /// <summary>
    /// Creates an LLM provider instance
    /// </summary>
    /// <param name="providerType">The type of LLM provider to create</param>
    /// <returns>An instance of the LLM provider</returns>
    public ILLMProvider CreateLLMProvider(LLMProviderType providerType = LLMProviderType.OpenAI)
    {
        try
        {
            return providerType switch
            {
                LLMProviderType.OpenAI => _serviceProvider.GetRequiredService<OpenAILLMProvider>(),
                _ => throw new ArgumentException($"Unsupported LLM provider type: {providerType}", nameof(providerType))
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create LLM provider of type {ProviderType}", providerType);
            throw;
        }
    }

    /// <summary>
    /// Creates a vector store instance
    /// </summary>
    /// <param name="storeType">The type of vector store to create</param>
    /// <returns>An instance of the vector store</returns>
    public IVectorStore CreateVectorStore(VectorStoreType storeType = VectorStoreType.InMemory)
    {
        try
        {
            return storeType switch
            {
                VectorStoreType.InMemory => _serviceProvider.GetRequiredService<IVectorStore>(),
                _ => throw new ArgumentException($"Unsupported vector store type: {storeType}", nameof(storeType))
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create vector store of type {StoreType}", storeType);
            throw;
        }
    }
}

/// <summary>
/// Enumeration of supported embedding provider types
/// </summary>
public enum EmbeddingProviderType
{
    OpenAI
}

/// <summary>
/// Enumeration of supported LLM provider types
/// </summary>
public enum LLMProviderType
{
    OpenAI
}

/// <summary>
/// Enumeration of supported vector store types
/// </summary>
public enum VectorStoreType
{
    InMemory
}