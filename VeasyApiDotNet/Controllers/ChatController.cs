using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using VeasyApi.Domain.Abstractions;
using VeasyApi.Models;

namespace VeasyApiDotNet.Controllers;

/// <summary>
/// Controller for chat completion and history management
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ChatController : ControllerBase
{
    private readonly IChatService _chatService;
    private readonly ILogger<ChatController> _logger;

    public ChatController(IChatService chatService, ILogger<ChatController> logger)
    {
        _chatService = chatService ?? throw new ArgumentNullException(nameof(chatService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Create a new chat completion
    /// </summary>
    /// <param name="request">The chat completion request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chat completion response</returns>
    [HttpPost("completions")]
    [ProducesResponseType(typeof(ChatCompletionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ChatCompletionResponse>> CreateChatCompletionAsync(
        [FromBody] ChatCompletionRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetUserId();
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized("User ID not found in token");
            }

            _logger.LogInformation("Creating chat completion for user {UserId}", userId);

            if (request.Stream)
            {
                return BadRequest("Streaming is not supported in this endpoint. Use /api/chat/completions/stream instead.");
            }

            var response = await _chatService.CreateChatCompletionAsync(request, userId, cancellationToken);

            if (!response.IsSuccessful)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Chat completion failed",
                    Detail = response.ErrorMessage,
                    Status = StatusCodes.Status400BadRequest
                });
            }

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating chat completion");
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal server error",
                Detail = "An error occurred while processing the chat completion",
                Status = StatusCodes.Status500InternalServerError
            });
        }
    }

    /// <summary>
    /// Create a streaming chat completion
    /// </summary>
    /// <param name="request">The chat completion request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Server-sent events stream</returns>
    [HttpPost("completions/stream")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CreateStreamingChatCompletionAsync(
        [FromBody] ChatCompletionRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetUserId();
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized("User ID not found in token");
            }

            _logger.LogInformation("Creating streaming chat completion for user {UserId}", userId);

            // Set response headers for Server-Sent Events
            Response.Headers.Add("Content-Type", "text/event-stream");
            Response.Headers.Add("Cache-Control", "no-cache");
            Response.Headers.Add("Connection", "keep-alive");

            // Stream the response
            await foreach (var chunk in _chatService.CreateStreamingChatCompletionAsync(request, userId, cancellationToken))
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await Response.WriteAsync(chunk, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }

            return new EmptyResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating streaming chat completion");
            await Response.WriteAsync($"data: {{\"error\": \"{ex.Message}\"}}\n\n", cancellationToken);
            return new EmptyResult();
        }
    }

    /// <summary>
    /// Get a specific chat history by ID
    /// </summary>
    /// <param name="chatId">The chat ID</param>
    /// <param name="includeMessages">Whether to include messages in the response</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chat history</returns>
    [HttpGet("{chatId:guid}")]
    [ProducesResponseType(typeof(ChatHistoryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ChatHistoryDto>> GetChatHistoryAsync(
        [FromRoute] Guid chatId,
        [FromQuery] bool includeMessages = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetUserId();
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized("User ID not found in token");
            }

            _logger.LogDebug("Getting chat history {ChatId} for user {UserId}", chatId, userId);

            var chatHistory = await _chatService.GetChatHistoryAsync(chatId, userId, includeMessages, cancellationToken);

            if (chatHistory == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Chat not found",
                    Detail = $"Chat with ID {chatId} was not found",
                    Status = StatusCodes.Status404NotFound
                });
            }

            return Ok(chatHistory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat history {ChatId}", chatId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal server error",
                Detail = "An error occurred while retrieving the chat history",
                Status = StatusCodes.Status500InternalServerError
            });
        }
    }

    /// <summary>
    /// Get paginated chat histories for the current user
    /// </summary>
    /// <param name="request">Pagination and filtering parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated chat histories</returns>
    [HttpGet("history")]
    [ProducesResponseType(typeof(ChatHistoryPagedResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ChatHistoryPagedResponse>> GetChatHistoriesAsync(
        [FromQuery] ChatHistoryRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetUserId();
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized("User ID not found in token");
            }

            _logger.LogDebug("Getting chat histories for user {UserId}, page {Page}", userId, request.Page);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var response = await _chatService.GetChatHistoriesAsync(userId, request, cancellationToken);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat histories");
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal server error",
                Detail = "An error occurred while retrieving chat histories",
                Status = StatusCodes.Status500InternalServerError
            });
        }
    }

    /// <summary>
    /// Get messages for a specific chat
    /// </summary>
    /// <param name="chatId">The chat ID</param>
    /// <param name="page">Page number (1-based)</param>
    /// <param name="pageSize">Number of messages per page</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of chat messages</returns>
    [HttpGet("{chatId:guid}/messages")]
    [ProducesResponseType(typeof(List<ChatMessageDetailDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<List<ChatMessageDetailDto>>> GetChatMessagesAsync(
        [FromRoute] Guid chatId,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetUserId();
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized("User ID not found in token");
            }

            _logger.LogDebug("Getting messages for chat {ChatId}, user {UserId}, page {Page}", chatId, userId, page);

            if (page < 1 || pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid pagination parameters",
                    Detail = "Page must be >= 1 and PageSize must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            var messages = await _chatService.GetChatMessagesAsync(chatId, userId, page, pageSize, cancellationToken);

            return Ok(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting messages for chat {ChatId}", chatId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal server error",
                Detail = "An error occurred while retrieving chat messages",
                Status = StatusCodes.Status500InternalServerError
            });
        }
    }

    /// <summary>
    /// Get the current user ID from the JWT token
    /// </summary>
    /// <returns>User ID or null if not found</returns>
    private string? GetUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
               User.FindFirst("sub")?.Value ??
               User.FindFirst("user_id")?.Value;
    }
}
