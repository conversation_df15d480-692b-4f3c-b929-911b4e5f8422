using Microsoft.AspNetCore.Mvc;
using VeasyApi.Models;
using VeasyApi.Domain.Abstractions;
using VeasyApi.Service.Factories;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;

namespace VeasyApiDotNet.Controllers;

/// <summary>
/// REST API controller for RAG (Retrieval-Augmented Generation) operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class RagController : ControllerBase
{
    private readonly ServiceFactory _serviceFactory;
    private readonly ILogger<RagController> _logger;

    public RagController(ServiceFactory serviceFactory, ILogger<RagController> logger)
    {
        _serviceFactory = serviceFactory ?? throw new ArgumentNullException(nameof(serviceFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Stores a document in the vector database
    /// </summary>
    /// <param name="document">The document to store</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The stored document with its assigned ID</returns>
    [HttpPost("documents")]
    [ProducesResponseType(typeof(Document), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Document>> StoreDocumentAsync([FromBody] Document document, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            _logger.LogInformation("Storing document with title: {Title}", document.Title);

            var embeddingProvider = _serviceFactory.CreateEmbeddingProvider();
            var vectorStore = _serviceFactory.CreateVectorStore();

            // Generate embedding for the document content
            var embedding = await embeddingProvider.GetEmbeddingAsync(document.Content, cancellationToken);

            // Store the document with its embedding
            document.Id = await vectorStore.StoreDocumentAsync(document, embedding, cancellationToken);
            document.UpdatedAt = DateTime.UtcNow;

            _logger.LogInformation("Successfully stored document with ID: {DocumentId}", document.Id);

            return CreatedAtAction(nameof(GetDocumentAsync), new { id = document.Id }, document);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store document");
            return StatusCode(500, "An error occurred while storing the document");
        }
    }

    /// <summary>
    /// Retrieves a document by its ID
    /// </summary>
    /// <param name="id">The document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The requested document</returns>
    [HttpGet("documents/{id}")]
    [ProducesResponseType(typeof(Document), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Document>> GetDocumentAsync(string id, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(id))
        {
            return BadRequest("Document ID is required");
        }

        try
        {
            var vectorStore = _serviceFactory.CreateVectorStore();
            var document = await vectorStore.GetDocumentAsync(id, cancellationToken);

            if (document == null)
            {
                return NotFound($"Document with ID '{id}' not found");
            }

            return Ok(document);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve document with ID: {DocumentId}", id);
            return StatusCode(500, "An error occurred while retrieving the document");
        }
    }

    /// <summary>
    /// Performs a RAG query to generate a response based on stored documents
    /// </summary>
    /// <param name="request">The RAG query request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The generated response with source documents</returns>
    [HttpPost("query")]
    [ProducesResponseType(typeof(RAGResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<RAGResponse>> QueryAsync([FromBody] RAGQueryRequest request, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Processing RAG query: {Query}", request.Query);

            var embeddingProvider = _serviceFactory.CreateEmbeddingProvider();
            var vectorStore = _serviceFactory.CreateVectorStore();
            var llmProvider = _serviceFactory.CreateLLMProvider();

            // Generate embedding for the query
            var queryEmbedding = await embeddingProvider.GetEmbeddingAsync(request.Query, cancellationToken);

            // Search for similar documents
            var similarDocuments = await vectorStore.SearchSimilarAsync(
                queryEmbedding, 
                request.TopK, 
                request.SimilarityThreshold, 
                cancellationToken);

            var retrievedDocs = similarDocuments.Select((doc, index) => new RetrievedDocument
            {
                Document = doc.document,
                SimilarityScore = doc.similarityScore,
                Rank = index + 1,
                RelevantExcerpt = TruncateContent(doc.document.Content, 200)
            }).ToList();

            // Build context from retrieved documents
            var context = string.Join("\n\n", retrievedDocs.Select(d => 
                $"Document: {d.Document.Title}\nContent: {d.Document.Content}"));

            // Generate response using LLM
            var systemMessage = "You are a helpful assistant. Use the provided context to answer the user's question. If the context doesn't contain relevant information, say so clearly.";
            var userMessage = $"Context:\n{context}\n\nQuestion: {request.Query}";

            var response = await llmProvider.GenerateResponseAsync(
                systemMessage, 
                userMessage, 
                request.MaxTokens, 
                request.Temperature, 
                cancellationToken);

            stopwatch.Stop();

            var ragResponse = new RAGResponse
            {
                Response = response,
                Query = request.Query,
                SourceDocuments = retrievedDocs,
                ConfidenceScore = CalculateConfidenceScore(retrievedDocs),
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                GeneratedAt = DateTime.UtcNow,
                IsSuccessful = true
            };

            _logger.LogInformation("Successfully processed RAG query in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

            return Ok(ragResponse);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Failed to process RAG query");
            
            return Ok(new RAGResponse
            {
                Query = request.Query,
                IsSuccessful = false,
                ErrorMessage = "An error occurred while processing the query",
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                GeneratedAt = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Deletes a document from the vector store
    /// </summary>
    /// <param name="id">The document ID to delete</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success status</returns>
    [HttpDelete("documents/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteDocumentAsync(string id, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(id))
        {
            return BadRequest("Document ID is required");
        }

        try
        {
            var vectorStore = _serviceFactory.CreateVectorStore();
            var deleted = await vectorStore.DeleteDocumentAsync(id, cancellationToken);

            if (!deleted)
            {
                return NotFound($"Document with ID '{id}' not found");
            }

            _logger.LogInformation("Successfully deleted document with ID: {DocumentId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete document with ID: {DocumentId}", id);
            return StatusCode(500, "An error occurred while deleting the document");
        }
    }

    /// <summary>
    /// Gets the total count of documents in the vector store
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The document count</returns>
    [HttpGet("documents/count")]
    [ProducesResponseType(typeof(long), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<long>> GetDocumentCountAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var vectorStore = _serviceFactory.CreateVectorStore();
            var count = await vectorStore.GetDocumentCountAsync(cancellationToken);
            return Ok(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get document count");
            return StatusCode(500, "An error occurred while retrieving document count");
        }
    }

    private static string TruncateContent(string content, int maxLength)
    {
        if (string.IsNullOrEmpty(content) || content.Length <= maxLength)
            return content;

        return content.Substring(0, maxLength) + "...";
    }

    private static float CalculateConfidenceScore(List<RetrievedDocument> documents)
    {
        if (!documents.Any())
            return 0.0f;

        // Simple confidence calculation based on average similarity score
        return documents.Average(d => d.SimilarityScore);
    }
}

/// <summary>
/// Request model for RAG queries
/// </summary>
public class RAGQueryRequest
{
    /// <summary>
    /// The query text to process
    /// </summary>
    [Required]
    [StringLength(2000, MinimumLength = 1)]
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// Number of similar documents to retrieve (default: 5)
    /// </summary>
    [Range(1, 20)]
    public int TopK { get; set; } = 5;

    /// <summary>
    /// Minimum similarity threshold for document retrieval (default: 0.0)
    /// </summary>
    [Range(0.0, 1.0)]
    public float SimilarityThreshold { get; set; } = 0.0f;

    /// <summary>
    /// Maximum tokens for LLM response generation (default: 1000)
    /// </summary>
    [Range(1, 4000)]
    public int MaxTokens { get; set; } = 1000;

    /// <summary>
    /// Temperature for LLM response generation (default: 0.7)
    /// </summary>
    [Range(0.0, 2.0)]
    public float Temperature { get; set; } = 0.7f;
}
