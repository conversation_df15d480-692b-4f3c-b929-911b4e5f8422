using Microsoft.EntityFrameworkCore;
using VeasyApi.Models;
using System.Text.Json;

namespace VeasyApiDotNet.Data;

/// <summary>
/// Database context for chat-related entities
/// </summary>
public class ChatDbContext : DbContext
{
    public ChatDbContext(DbContextOptions<ChatDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Chat histories table
    /// </summary>
    public DbSet<ChatHistory> ChatHistories { get; set; }

    /// <summary>
    /// Chat messages table
    /// </summary>
    public DbSet<ChatMessage> ChatMessages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        ConfigureChatHistory(modelBuilder);
        ConfigureChatMessage(modelBuilder);
    }

    private static void ConfigureChatHistory(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ChatHistory>();

        // Primary key
        entity.HasKey(e => e.ChatId);

        // Table name
        entity.ToTable("ChatHistories");

        // Properties
        entity.Property(e => e.ChatId)
            .HasDefaultValueSql("gen_random_uuid()")
            .ValueGeneratedOnAdd();

        entity.Property(e => e.UserId)
            .IsRequired()
            .HasMaxLength(256);

        entity.Property(e => e.Title)
            .IsRequired()
            .HasMaxLength(500);

        entity.Property(e => e.Description)
            .HasMaxLength(2000);

        entity.Property(e => e.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.IsActive)
            .HasDefaultValue(true);

        entity.Property(e => e.MessageCount)
            .HasDefaultValue(0);

        entity.Property(e => e.TotalTokenCount)
            .HasDefaultValue(0);

        // JSON column for metadata
        entity.Property(e => e.Metadata)
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>()
            );

        // Indexes
        entity.HasIndex(e => e.UserId)
            .HasDatabaseName("IX_ChatHistories_UserId");

        entity.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_ChatHistories_CreatedAt");

        entity.HasIndex(e => e.UpdatedAt)
            .HasDatabaseName("IX_ChatHistories_UpdatedAt");

        entity.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_ChatHistories_IsActive");

        entity.HasIndex(e => new { e.UserId, e.IsActive })
            .HasDatabaseName("IX_ChatHistories_UserId_IsActive");

        // Relationships
        entity.HasMany(e => e.Messages)
            .WithOne(e => e.Chat)
            .HasForeignKey(e => e.ChatId)
            .OnDelete(DeleteBehavior.Cascade);
    }

    private static void ConfigureChatMessage(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ChatMessage>();

        // Primary key
        entity.HasKey(e => e.MessageId);

        // Table name
        entity.ToTable("ChatMessages");

        // Properties
        entity.Property(e => e.MessageId)
            .HasDefaultValueSql("gen_random_uuid()")
            .ValueGeneratedOnAdd();

        entity.Property(e => e.ChatId)
            .IsRequired();

        entity.Property(e => e.Role)
            .IsRequired()
            .HasMaxLength(50);

        entity.Property(e => e.Content)
            .IsRequired()
            .HasColumnType("text");

        entity.Property(e => e.Timestamp)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        entity.Property(e => e.TokenCount)
            .HasDefaultValue(0);

        entity.Property(e => e.Name)
            .HasMaxLength(256);

        entity.Property(e => e.ToolCallId)
            .HasMaxLength(256);

        entity.Property(e => e.IsActive)
            .HasDefaultValue(true);

        entity.Property(e => e.SequenceNumber)
            .HasDefaultValue(0);

        // JSON columns
        entity.Property(e => e.FunctionCall)
            .HasColumnType("jsonb")
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null)
            );

        entity.Property(e => e.ToolCalls)
            .HasColumnType("jsonb")
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<List<Dictionary<string, object>>>(v, (JsonSerializerOptions?)null)
            );

        entity.Property(e => e.Metadata)
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>()
            );

        // Indexes
        entity.HasIndex(e => e.ChatId)
            .HasDatabaseName("IX_ChatMessages_ChatId");

        entity.HasIndex(e => e.Timestamp)
            .HasDatabaseName("IX_ChatMessages_Timestamp");

        entity.HasIndex(e => e.Role)
            .HasDatabaseName("IX_ChatMessages_Role");

        entity.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_ChatMessages_IsActive");

        entity.HasIndex(e => new { e.ChatId, e.SequenceNumber })
            .HasDatabaseName("IX_ChatMessages_ChatId_SequenceNumber");

        entity.HasIndex(e => new { e.ChatId, e.Timestamp })
            .HasDatabaseName("IX_ChatMessages_ChatId_Timestamp");

        // Foreign key relationship
        entity.HasOne(e => e.Chat)
            .WithMany(e => e.Messages)
            .HasForeignKey(e => e.ChatId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
