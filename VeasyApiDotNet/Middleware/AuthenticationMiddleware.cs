using Microsoft.Extensions.Options;
using System.Text.Json;

namespace VeasyApiDotNet.Middleware;

/// <summary>
/// Custom authentication middleware for API key validation
/// </summary>
public class AuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<AuthenticationMiddleware> _logger;
    private readonly AuthenticationOptions _options;

    public AuthenticationMiddleware(
        RequestDelegate next,
        ILogger<AuthenticationMiddleware> logger,
        IOptions<AuthenticationOptions> options)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Skip authentication for health checks and swagger endpoints
        if (ShouldSkipAuthentication(context.Request.Path))
        {
            await _next(context);
            return;
        }

        if (!_options.IsEnabled)
        {
            _logger.LogDebug("Authentication is disabled, skipping validation");
            await _next(context);
            return;
        }

        try
        {
            if (!ValidateApiKeyAsync(context).Result)
            {
                await HandleUnauthorizedAsync(context);
                return;
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during authentication");
            await HandleInternalErrorAsync(context);
        }
    }

    private Task<bool> ValidateApiKeyAsync(HttpContext context)
    {
        // Try to get API key from header
        if (context.Request.Headers.TryGetValue(_options.ApiKeyHeaderName, out var headerValue))
        {
            var apiKey = headerValue.FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(apiKey))
            {
                var isValid = _options.ValidApiKeys.Contains(apiKey);
                if (isValid)
                {
                    _logger.LogDebug("Valid API key provided in header");
                    return Task.FromResult(true);
                }
                else
                {
                    _logger.LogWarning("Invalid API key provided in header");
                }
            }
        }

        // Try to get API key from query parameter
        if (context.Request.Query.TryGetValue(_options.ApiKeyQueryParameterName, out var queryValue))
        {
            var apiKey = queryValue.FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(apiKey))
            {
                var isValid = _options.ValidApiKeys.Contains(apiKey);
                if (isValid)
                {
                    _logger.LogDebug("Valid API key provided in query parameter");
                    return Task.FromResult(true);
                }
                else
                {
                    _logger.LogWarning("Invalid API key provided in query parameter");
                }
            }
        }

        _logger.LogWarning("No valid API key found in request from {RemoteIpAddress}",
            context.Connection.RemoteIpAddress);

        return Task.FromResult(false);
    }

    private static bool ShouldSkipAuthentication(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant() ?? string.Empty;
        
        return pathValue.StartsWith("/health") ||
               pathValue.StartsWith("/swagger") ||
               pathValue.StartsWith("/api-docs") ||
               pathValue == "/" ||
               pathValue == "/favicon.ico";
    }

    private async Task HandleUnauthorizedAsync(HttpContext context)
    {
        context.Response.StatusCode = 401;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "Unauthorized",
            message = "Valid API key is required",
            timestamp = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);
    }

    private async Task HandleInternalErrorAsync(HttpContext context)
    {
        context.Response.StatusCode = 500;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "Internal Server Error",
            message = "An error occurred during authentication",
            timestamp = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);
    }
}

/// <summary>
/// Configuration options for authentication middleware
/// </summary>
public class AuthenticationOptions
{
    public const string SectionName = "Authentication";

    /// <summary>
    /// Whether authentication is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// The header name to look for the API key
    /// </summary>
    public string ApiKeyHeaderName { get; set; } = "X-API-Key";

    /// <summary>
    /// The query parameter name to look for the API key
    /// </summary>
    public string ApiKeyQueryParameterName { get; set; } = "apikey";

    /// <summary>
    /// List of valid API keys
    /// </summary>
    public List<string> ValidApiKeys { get; set; } = new();
}
