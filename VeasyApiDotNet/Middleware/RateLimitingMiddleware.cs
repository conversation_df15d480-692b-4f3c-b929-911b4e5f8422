using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Net;
using System.Security.Claims;
using System.Text.Json;

namespace VeasyApiDotNet.Middleware;

/// <summary>
/// Middleware for enforcing rate limits on API endpoints
/// </summary>
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RateLimitingMiddleware> _logger;
    private readonly RateLimitingOptions _options;
    
    // In-memory stores for rate limiting counters
    private static readonly ConcurrentDictionary<string, RateLimitCounter> _userRateLimits = new();
    private static readonly ConcurrentDictionary<string, RateLimitCounter> _ipRateLimits = new();
    
    public RateLimitingMiddleware(
        RequestDelegate next,
        ILogger<RateLimitingMiddleware> logger,
        IOptions<RateLimitingOptions> options)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Skip rate limiting for health checks and swagger endpoints
        if (ShouldSkipRateLimiting(context.Request.Path))
        {
            await _next(context);
            return;
        }

        if (!_options.IsEnabled)
        {
            _logger.LogDebug("Rate limiting is disabled, skipping validation");
            await _next(context);
            return;
        }

        try
        {
            // Get the appropriate rate limit
            var endpoint = context.Request.Path.Value?.ToLowerInvariant() ?? string.Empty;
            var rateLimit = GetRateLimitForEndpoint(endpoint);

            // Check if rate limit has been exceeded
            string identifier = GetClientIdentifier(context);
            
            if (IsRateLimitExceeded(identifier, rateLimit, out var counter))
            {
                await HandleRateLimitExceededAsync(context, counter, rateLimit);
                return;
            }

            // Add rate limit headers
            AddRateLimitHeaders(context, counter, rateLimit);

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during rate limiting");
            await _next(context);
        }
    }

    private static bool ShouldSkipRateLimiting(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant() ?? string.Empty;
        
        return pathValue.StartsWith("/health") ||
               pathValue.StartsWith("/swagger") ||
               pathValue.StartsWith("/api-docs") ||
               pathValue == "/" ||
               pathValue == "/favicon.ico";
    }

    private string GetClientIdentifier(HttpContext context)
    {
        // Try to get user ID from claims for authenticated users
        var userId = GetUserIdFromClaims(context);
        if (!string.IsNullOrEmpty(userId))
        {
            return $"user:{userId}";
        }

        // Fallback to IP address for unauthenticated requests
        var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        return $"ip:{ipAddress}";
    }

    private string GetUserIdFromClaims(HttpContext context)
    {
        // Check JWT claims first
        if (context.User?.Identity?.IsAuthenticated == true)
        {
            var userIdClaim = context.User.FindFirst(_options.UserIdClaimType) ??
                              context.User.FindFirst(ClaimTypes.NameIdentifier);
            
            if (userIdClaim != null)
            {
                return userIdClaim.Value;
            }
        }

        // If JWT auth failed, check for API key (if it exists in HttpContext.Items)
        if (context.Items.TryGetValue("ApiKey", out var apiKey) && apiKey is string key)
        {
            return $"apikey:{key}";
        }

        return string.Empty;
    }

    private RateLimit GetRateLimitForEndpoint(string endpoint)
    {
        // Check for chat completion endpoints
        if (endpoint.Contains("/api/chat/completions"))
        {
            return _options.ChatCompletionRateLimit;
        }

        // Check for chat history endpoints
        if (endpoint.Contains("/api/chat/history"))
        {
            return _options.ChatHistoryRateLimit;
        }

        // Default to general API rate limit
        return _options.GeneralApiRateLimit;
    }

    private bool IsRateLimitExceeded(string identifier, RateLimit rateLimit, out RateLimitCounter counter)
    {
        var isUserIdentifier = identifier.StartsWith("user:");
        var dictionary = isUserIdentifier ? _userRateLimits : _ipRateLimits;
        
        counter = dictionary.GetOrAdd(identifier, _ => new RateLimitCounter());
        
        lock (counter)
        {
            // Reset counter if time window has passed
            var now = DateTimeOffset.UtcNow;
            if (now >= counter.WindowResetTime)
            {
                var windowDuration = TimeSpan.FromSeconds(rateLimit.WindowInSeconds);
                counter.WindowResetTime = now.Add(windowDuration);
                counter.RequestCount = 0;
            }

            // Check if limit has been exceeded
            var requestLimit = isUserIdentifier ? rateLimit.RequestLimit : _options.IpRateLimit.RequestLimit;
            if (counter.RequestCount >= requestLimit)
            {
                _logger.LogWarning("Rate limit exceeded for {Identifier}. Limit: {Limit}, Time window: {Window}s",
                    identifier, requestLimit, rateLimit.WindowInSeconds);
                return true;
            }

            // Increment the counter
            counter.RequestCount++;
            return false;
        }
    }

    private static void AddRateLimitHeaders(HttpContext context, RateLimitCounter counter, RateLimit rateLimit)
    {
        var responseHeaders = context.Response.Headers;
        
        responseHeaders["X-RateLimit-Limit"] = rateLimit.RequestLimit.ToString();
        responseHeaders["X-RateLimit-Remaining"] = Math.Max(0, rateLimit.RequestLimit - counter.RequestCount).ToString();
        
        var resetTime = counter.WindowResetTime.ToUnixTimeSeconds();
        responseHeaders["X-RateLimit-Reset"] = resetTime.ToString();
    }

    private async Task HandleRateLimitExceededAsync(HttpContext context, RateLimitCounter counter, RateLimit rateLimit)
    {
        context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
        context.Response.ContentType = "application/json";
        
        // Calculate seconds until reset
        var secondsUntilReset = Math.Max(1, (int)(counter.WindowResetTime - DateTimeOffset.UtcNow).TotalSeconds);
        
        // Set retry-after header
        context.Response.Headers["Retry-After"] = secondsUntilReset.ToString();
        
        // Add rate limit headers
        AddRateLimitHeaders(context, counter, rateLimit);
        
        var response = new
        {
            error = "Too Many Requests",
            message = $"Rate limit exceeded. Try again in {secondsUntilReset} seconds.",
            retryAfter = secondsUntilReset,
            timestamp = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);
    }
}

/// <summary>
/// Tracks rate limit information for a specific client
/// </summary>
public class RateLimitCounter
{
    public int RequestCount { get; set; } = 0;
    public DateTimeOffset WindowResetTime { get; set; } = DateTimeOffset.UtcNow.AddMinutes(1);
}

/// <summary>
/// Defines a rate limit rule
/// </summary>
public class RateLimit
{
    /// <summary>
    /// Maximum number of requests allowed in the time window
    /// </summary>
    public int RequestLimit { get; set; }
    
    /// <summary>
    /// Time window in seconds
    /// </summary>
    public int WindowInSeconds { get; set; } = 60;
}

/// <summary>
/// Configuration options for rate limiting middleware
/// </summary>
public class RateLimitingOptions
{
    public const string SectionName = "RateLimiting";

    /// <summary>
    /// Whether rate limiting is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Claim type that contains the user ID
    /// </summary>
    public string UserIdClaimType { get; set; } = "sub";

    /// <summary>
    /// Rate limit for chat completion endpoints
    /// </summary>
    public RateLimit ChatCompletionRateLimit { get; set; } = new()
    {
        RequestLimit = 10,
        WindowInSeconds = 60
    };

    /// <summary>
    /// Rate limit for chat history endpoints
    /// </summary>
    public RateLimit ChatHistoryRateLimit { get; set; } = new()
    {
        RequestLimit = 30,
        WindowInSeconds = 60
    };

    /// <summary>
    /// Rate limit for general API endpoints
    /// </summary>
    public RateLimit GeneralApiRateLimit { get; set; } = new()
    {
        RequestLimit = 60,
        WindowInSeconds = 60
    };

    /// <summary>
    /// Rate limit for IP-based fallback
    /// </summary>
    public RateLimit IpRateLimit { get; set; } = new()
    {
        RequestLimit = 100,
        WindowInSeconds = 60
    };

    /// <summary>
    /// Optional: Redis connection string for distributed rate limiting in production
    /// </summary>
    public string? RedisConnectionString { get; set; }

    /// <summary>
    /// Whether to use Redis for distributed rate limiting (if connection string is provided)
    /// </summary>
    public bool UseRedis { get; set; } = false;
}