﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using VeasyApi.Service.Data;

#nullable disable

namespace VeasyApiDotNet.Migrations
{
    [DbContext(typeof(ChatDbContext))]
    [Migration("20250702160036_InitialChatSchema")]
    partial class InitialChatSchema
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("VeasyApi.Models.ChatHistory", b =>
                {
                    b.Property<Guid>("ChatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int>("MessageCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("TotalTokenCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("ChatId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_ChatHistories_CreatedAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ChatHistories_IsActive");

                    b.HasIndex("UpdatedAt")
                        .HasDatabaseName("IX_ChatHistories_UpdatedAt");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_ChatHistories_UserId");

                    b.HasIndex("UserId", "IsActive")
                        .HasDatabaseName("IX_ChatHistories_UserId_IsActive");

                    b.ToTable("ChatHistories", (string)null);
                });

            modelBuilder.Entity("VeasyApi.Models.ChatMessage", b =>
                {
                    b.Property<Guid>("MessageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<Guid>("ChatId")
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FunctionCall")
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("SequenceNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("Timestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("TokenCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("ToolCallId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ToolCalls")
                        .HasColumnType("jsonb");

                    b.HasKey("MessageId");

                    b.HasIndex("ChatId")
                        .HasDatabaseName("IX_ChatMessages_ChatId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ChatMessages_IsActive");

                    b.HasIndex("Role")
                        .HasDatabaseName("IX_ChatMessages_Role");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("IX_ChatMessages_Timestamp");

                    b.HasIndex("ChatId", "SequenceNumber")
                        .HasDatabaseName("IX_ChatMessages_ChatId_SequenceNumber");

                    b.HasIndex("ChatId", "Timestamp")
                        .HasDatabaseName("IX_ChatMessages_ChatId_Timestamp");

                    b.ToTable("ChatMessages", (string)null);
                });

            modelBuilder.Entity("VeasyApi.Models.ChatMessage", b =>
                {
                    b.HasOne("VeasyApi.Models.ChatHistory", "Chat")
                        .WithMany("Messages")
                        .HasForeignKey("ChatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Chat");
                });

            modelBuilder.Entity("VeasyApi.Models.ChatHistory", b =>
                {
                    b.Navigation("Messages");
                });
#pragma warning restore 612, 618
        }
    }
}
