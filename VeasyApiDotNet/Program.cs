using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using OpenAI;
using Scalar.AspNetCore;
using VeasyApi.Domain.Abstractions;
using VeasyApi.Models;
using VeasyApi.Service.Implementations;
using VeasyApi.Service.Data;
using VeasyApiDotNet.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddOpenApi();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Configure Entity Framework with PostgreSQL
builder.Services.AddDbContext<ChatDbContext>(options =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    options.UseNpgsql(connectionString, b => b.MigrationsAssembly("VeasyApiDotNet"));
});

// Configure options
builder.Services.Configure<OpenAILLMOptions>(
    builder.Configuration.GetSection(OpenAILLMOptions.SectionName));
builder.Services.Configure<JwtAuthenticationOptions>(
    builder.Configuration.GetSection(JwtAuthenticationOptions.SectionName));
builder.Services.Configure<AuthenticationOptions>(
    builder.Configuration.GetSection(AuthenticationOptions.SectionName));
builder.Services.Configure<RateLimitingOptions>(
    builder.Configuration.GetSection(RateLimitingOptions.SectionName));

// Configure OpenAI client
builder.Services.AddSingleton<OpenAIClient>(serviceProvider =>
{
    var configuration = serviceProvider.GetRequiredService<IConfiguration>();
    var apiKey = configuration["OpenAI:ApiKey"];

    if (string.IsNullOrWhiteSpace(apiKey))
    {
        throw new InvalidOperationException("OpenAI API key is not configured. Please set the 'OpenAI:ApiKey' configuration value.");
    }

    return new OpenAIClient(apiKey);
});

// Register chat services
builder.Services.AddScoped<IChatService, ChatService>();

// Configure JWT Bearer Authentication
var jwtOptions = builder.Configuration.GetSection(JwtAuthenticationOptions.SectionName).Get<JwtAuthenticationOptions>();
if (jwtOptions?.IsEnabled == true && !string.IsNullOrEmpty(jwtOptions.Authority))
{
    builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            options.Authority = jwtOptions.Authority;
            options.Audience = jwtOptions.Audience;
            options.RequireHttpsMetadata = jwtOptions.RequireHttpsMetadata;
            options.SaveToken = jwtOptions.SaveToken;

            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = jwtOptions.ValidateIssuer,
                ValidateAudience = jwtOptions.ValidateAudience,
                ValidateLifetime = jwtOptions.ValidateLifetime,
                ValidateIssuerSigningKey = jwtOptions.ValidateIssuerSigningKey,
                ClockSkew = TimeSpan.FromMinutes(jwtOptions.ClockSkewMinutes),
                NameClaimType = jwtOptions.UserNameClaimType,
                RoleClaimType = jwtOptions.RoleClaimType
            };

            // Handle authentication events
            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    logger.LogError(context.Exception, "JWT authentication failed");
                    return Task.CompletedTask;
                },
                OnTokenValidated = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    logger.LogDebug("JWT token validated for user: {UserId}",
                        context.Principal?.FindFirst(jwtOptions.UserIdClaimType)?.Value);
                    return Task.CompletedTask;
                }
            };
        });

    builder.Services.AddAuthorization();
}

// Add logging
builder.Services.AddLogging();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference();
}

app.UseHttpsRedirection();

// Use CORS
app.UseCors();

// Use authentication and authorization
var authOptions = app.Configuration.GetSection(JwtAuthenticationOptions.SectionName).Get<JwtAuthenticationOptions>();
if (authOptions?.IsEnabled == true && !string.IsNullOrEmpty(authOptions.Authority))
{
    app.UseAuthentication();
    app.UseAuthorization();
}
else
{
    // Fallback to custom authentication middleware for API key authentication
    app.UseMiddleware<AuthenticationMiddleware>();
}

// Add rate limiting middleware after authentication but before controllers
app.UseMiddleware<RateLimitingMiddleware>();

// Map controllers
app.MapControllers();

// Add health check endpoint
app.MapGet("/health", () => new { Status = "Healthy", Timestamp = DateTime.UtcNow })
    .WithName("HealthCheck");

app.Run();
