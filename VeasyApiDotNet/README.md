# VeasyApiDotNet - RAG API

A RESTful API for Retrieval-Augmented Generation (RAG) operations built with ASP.NET Core 8.0 and OpenAI services.

## Features

- **Document Storage**: Store documents with automatic vector embedding generation
- **Similarity Search**: Find similar documents using vector similarity
- **RAG Queries**: Generate responses using retrieved documents as context
- **OpenAI Integration**: Uses OpenAI's embedding and language models
- **API Authentication**: Optional API key-based authentication
- **Swagger Documentation**: Interactive API documentation
- **CORS Support**: Cross-origin resource sharing enabled
- **Health Checks**: Built-in health monitoring endpoint

## Project Structure

```
VeasyApiDotNet/
├── Controllers/
│   └── RagController.cs          # REST API controller for RAG operations
├── Services/
│   ├── Abstractions/             # Interface definitions
│   │   ├── IEmbeddingProvider.cs # Interface for text embedding services
│   │   ├── IVectorStore.cs       # Interface for vector database operations
│   │   └── ILLMProvider.cs       # Interface for Large Language Model services
│   ├── Implementations/          # Concrete service implementations
│   │   ├── OpenAIEmbeddingProvider.cs  # OpenAI embedding implementation
│   │   ├── OpenAILLMProvider.cs        # OpenAI LLM implementation
│   │   └── InMemoryVectorStore.cs      # In-memory vector store
│   └── Factories/
│       └── ServiceFactory.cs     # Factory pattern for service creation
├── Models/                       # Data models and DTOs
│   ├── Document.cs              # Document entity/model
│   └── RAGResponse.cs           # Response model for RAG operations
└── Middleware/
    └── AuthenticationMiddleware.cs  # Custom authentication middleware
```

## Configuration

Update `appsettings.json` with your OpenAI API key:

```json
{
  "OpenAI": {
    "ApiKey": "your-openai-api-key-here",
    "Embedding": {
      "ModelName": "text-embedding-3-small",
      "EmbeddingDimension": 1536
    },
    "LLM": {
      "ModelName": "gpt-4o-mini",
      "MaxContextLength": 128000
    }
  },
  "Authentication": {
    "IsEnabled": false,
    "ApiKeyHeaderName": "X-API-Key",
    "ApiKeyQueryParameterName": "apikey",
    "ValidApiKeys": [
      "your-api-key-1",
      "your-api-key-2"
    ]
  }
}
```

## Getting Started

1. **Prerequisites**
   - .NET 8.0 SDK
   - OpenAI API key

2. **Installation**
   ```bash
   git clone <repository-url>
   cd VeasyApiDotNet
   dotnet restore
   ```

3. **Configuration**
   - Update `appsettings.json` with your OpenAI API key
   - Optionally enable authentication by setting `Authentication:IsEnabled` to `true`

4. **Run the Application**
   ```bash
   dotnet run
   ```

5. **Access the API**
   - Swagger UI: `http://localhost:5134`
   - Health Check: `http://localhost:5134/health`

## API Endpoints

### Documents
- `POST /api/rag/documents` - Store a new document
- `GET /api/rag/documents/{id}` - Retrieve a document by ID
- `DELETE /api/rag/documents/{id}` - Delete a document
- `GET /api/rag/documents/count` - Get total document count

### RAG Operations
- `POST /api/rag/query` - Perform a RAG query

### System
- `GET /health` - Health check endpoint

## Usage Examples

### Store a Document
```bash
curl -X POST "http://localhost:5134/api/rag/documents" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Sample Document",
    "content": "This is a sample document content for testing RAG operations.",
    "tags": ["sample", "test"]
  }'
```

### Perform a RAG Query
```bash
curl -X POST "http://localhost:5134/api/rag/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is this document about?",
    "topK": 5,
    "similarityThreshold": 0.1,
    "maxTokens": 1000,
    "temperature": 0.7
  }'
```

## Development

### Building
```bash
dotnet build
```

### Running Tests
```bash
dotnet test
```

### Publishing
```bash
dotnet publish -c Release
```

## Architecture

The application follows clean architecture principles with:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and external service integrations
- **Models**: Data transfer objects and entities
- **Middleware**: Cross-cutting concerns like authentication
- **Dependency Injection**: Configured in `Program.cs`

## Technologies Used

- ASP.NET Core 8.0
- OpenAI .NET SDK
- Swagger/OpenAPI
- Microsoft Extensions (Logging, DI, Configuration)
- System.ComponentModel.Annotations

## License

This project is licensed under the MIT License.
