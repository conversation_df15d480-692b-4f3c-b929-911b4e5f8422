{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=veasy_chat_dev;Username=********;Password=********;Port=5432"}, "OpenAI": {"ApiKey": "your-development-openai-api-key-here"}, "Authentication": {"Jwt": {"Authority": "https://your-identity-server-dev.com", "Audience": "veasy-api-dev", "RequireHttpsMetadata": false}}, "RateLimiting": {"IsEnabled": true, "UserIdClaimType": "sub", "ChatCompletionRateLimit": {"RequestLimit": 30, "WindowInSeconds": 60}, "ChatHistoryRateLimit": {"RequestLimit": 60, "WindowInSeconds": 60}, "GeneralApiRateLimit": {"RequestLimit": 120, "WindowInSeconds": 60}, "IpRateLimit": {"RequestLimit": 200, "WindowInSeconds": 60}, "UseRedis": false}}