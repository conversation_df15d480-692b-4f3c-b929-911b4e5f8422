{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=veasy_chat;Username=;Password=;Port=5432"}, "OpenAI": {"ApiKey": "", "Embedding": {"ModelName": "text-embedding-3-small", "EmbeddingDimension": 1536}, "LLM": {"ModelName": "gpt-4o-mini", "MaxContextLength": 128000, "DefaultMaxTokens": 1000, "DefaultTemperature": 0.7, "DefaultTopP": 1.0, "DefaultFrequencyPenalty": 0.0, "DefaultPresencePenalty": 0.0, "EnableFunctionCalling": true, "EnableStreaming": true, "TimeoutSeconds": 120, "MaxRetryAttempts": 3, "RetryDelayMs": 1000}}, "Authentication": {"IsEnabled": true, "ApiKeyHeaderName": "X-API-Key", "ApiKeyQueryParameterName": "apikey", "ValidApiKeys": ["your-api-key-1", "your-api-key-2"], "Jwt": {"Authority": "https://sso.veasy.vn", "Audience": "veasy-chatbot-api", "RequireHttpsMetadata": true, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ClockSkewMinutes": 5, "SaveToken": true, "UserIdClaimType": "sub", "UserNameClaimType": "name", "EmailClaimType": "email", "RoleClaimType": "role", "RequiredScopes": [], "IsEnabled": true, "ExcludedPaths": ["/health", "/swagger", "/scalar", "/.well-known"], "ClaimMappings": {}}}}